#!/usr/bin/env python3
"""
Script per testare direttamente l'API del report BOQ
"""

import requests
import json
import sys

def test_boq_api():
    """Testa direttamente l'endpoint API del BOQ."""
    
    print("=== TEST API BOQ ===\n")
    
    # URL dell'API BOQ
    base_url = "http://localhost:8001"
    cantiere_id = 1
    
    # Test endpoint BOQ (con formato video per ottenere i dati JSON)
    boq_url = f"{base_url}/api/reports/{cantiere_id}/boq?formato=video"
    
    try:
        print(f"🔗 Chiamando: {boq_url}")
        response = requests.get(boq_url, timeout=10)
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Risposta ricevuta!")
            print(f"Nome cantiere: {data.get('nome_cantiere', 'N/A')}")
            
            distinta = data.get('distinta_materiali', [])
            print(f"Numero categorie: {len(distinta)}")
            
            print("\n📋 DISTINTA MATERIALI:")
            print("-" * 80)
            
            # Cerca il caso specifico FG16OR16 1X240MM2
            caso_trovato = False
            
            for item in distinta:
                tipologia = item.get('tipologia', 'N/A')
                formazione = item.get('formazione', 'N/A')
                metri_acquistati = item.get('metri_acquistati', 0)
                metri_residui = item.get('metri_residui', 0)
                metri_da_posare = item.get('metri_da_posare', 0)
                metri_mancanti = item.get('metri_mancanti', 0)
                necessita_acquisto = item.get('necessita_acquisto', True)
                
                print(f"{tipologia} | {formazione}")
                print(f"  Metri acquistati: {metri_acquistati}")
                print(f"  Metri residui: {metri_residui}")
                print(f"  Metri da posare: {metri_da_posare}")
                print(f"  Metri mancanti: {metri_mancanti}")
                print(f"  Necessita acquisto: {'Sì' if necessita_acquisto else 'No'}")
                
                # Verifica caso specifico
                if tipologia == "FG16OR16" and "240MM2" in formazione:
                    caso_trovato = True
                    print(f"  *** CASO SPECIFICO TROVATO! ***")
                    print(f"  🎯 Risultato API:")
                    print(f"    - Metri acquistati: {metri_acquistati}m")
                    print(f"    - Metri residui: {metri_residui}m")
                    print(f"    - Metri da posare: {metri_da_posare}m")
                    print(f"    - Metri mancanti: {metri_mancanti}m")
                    print(f"    - Necessita acquisto: {'Sì' if necessita_acquisto else 'No'}")
                    
                    if metri_residui > 0 and not necessita_acquisto:
                        print(f"    🎉 API CORRETTA: Bobina riconosciuta!")
                    else:
                        print(f"    ❌ API ERRATA: Bobina non riconosciuta!")
                
                print()
            
            if not caso_trovato:
                print("❌ CASO SPECIFICO FG16OR16 1X240MM2 NON TROVATO!")
            
            # Riepilogo
            riepilogo = data.get('riepilogo', {})
            print(f"\n📊 RIEPILOGO:")
            print(f"  Totale metri acquistati: {riepilogo.get('totale_metri_acquistati', 0)}m")
            print(f"  Totale metri residui: {riepilogo.get('totale_metri_residui', 0)}m")
            print(f"  Totale metri mancanti: {riepilogo.get('totale_metri_mancanti', 0)}m")
            print(f"  Categorie che necessitano acquisto: {riepilogo.get('categorie_necessitano_acquisto', 0)}")
            
        else:
            print(f"❌ Errore API: {response.status_code}")
            print(f"Risposta: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Errore: Impossibile connettersi al backend!")
        print("Assicurati che il backend sia in esecuzione su http://localhost:8001")
    except requests.exceptions.Timeout:
        print("❌ Errore: Timeout della richiesta!")
    except Exception as e:
        print(f"❌ Errore imprevisto: {e}")

def test_server_status():
    """Verifica se il server è attivo."""
    try:
        response = requests.get("http://localhost:8001/docs", timeout=5)
        if response.status_code == 200:
            print("✅ Backend attivo su http://localhost:8001")
            return True
        else:
            print(f"⚠️ Backend risponde ma con status {response.status_code}")
            return False
    except:
        print("❌ Backend non raggiungibile su http://localhost:8001")
        return False

if __name__ == "__main__":
    print("🔍 Verificando stato del server...")
    if test_server_status():
        print()
        test_boq_api()
    else:
        print("\n💡 Suggerimenti:")
        print("1. Avvia il backend con: python webapp/run_system_simple.py")
        print("2. Oppure verifica che sia in esecuzione sulla porta 8001")
