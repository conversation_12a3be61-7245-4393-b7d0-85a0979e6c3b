#!/usr/bin/env python3
"""
Script per testare le query SQLAlchemy nel report BOQ
"""

import sys
import os

# Aggiungi il percorso del backend al PYTHONPATH
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_sqlalchemy_queries():
    """Testa le query SQLAlchemy per il report BOQ."""
    
    print("=== TEST QUERY SQLALCHEMY ===\n")
    
    try:
        from backend.database import get_db
        from sqlalchemy import text
        
        # Ottieni una sessione del database
        db_session = next(get_db())
        cantiere_id = 1
        
        print("✅ Sessione database ottenuta")
        
        # Test 1: Query cantiere
        print("\n🔍 TEST 1: QUERY CANTIERE")
        print("-" * 40)
        
        cantiere_result = db_session.execute(
            text("SELECT nome FROM cantieri WHERE id_cantiere = :cantiere_id"),
            {"cantiere_id": cantiere_id}
        ).fetchone()
        
        print(f"Risultato cantiere: {cantiere_result}")
        if cantiere_result:
            print(f"Nome cantiere: {cantiere_result[0]}")
        else:
            print("❌ Cantiere non trovato!")
        
        # Test 2: Query cavi
        print("\n🔍 TEST 2: QUERY CAVI")
        print("-" * 40)
        
        cavi_query = text("""
            SELECT
                tipologia,
                sezione as formazione,
                COUNT(*) as num_cavi,
                COUNT(CASE WHEN stato_installazione != 'Installato' THEN 1 END) as num_cavi_rimanenti,
                SUM(metri_teorici) as metri_teorici_totali,
                SUM(CASE WHEN stato_installazione = 'Installato' THEN metratura_reale ELSE 0 END) as metri_reali_posati,
                SUM(CASE WHEN stato_installazione != 'Installato' THEN metri_teorici ELSE 0 END) as metri_da_posare
            FROM cavi
            WHERE id_cantiere = :cantiere_id AND (modificato_manualmente IS NULL OR modificato_manualmente != 3)
            GROUP BY tipologia, sezione
            ORDER BY tipologia, sezione
        """)
        
        cavi_result = db_session.execute(cavi_query, {"cantiere_id": cantiere_id}).fetchall()
        
        print(f"Numero righe cavi: {len(cavi_result)}")
        for i, row in enumerate(cavi_result):
            print(f"  Riga {i+1}: {row}")
            print(f"    Tipologia: {row[0]}")
            print(f"    Formazione: {row[1]}")
            print(f"    Num cavi: {row[2]}")
            print(f"    Metri da posare: {row[6]}")
        
        # Test 3: Query bobine
        print("\n🔍 TEST 3: QUERY BOBINE")
        print("-" * 40)
        
        bobine_query = text("""
            SELECT
                tipologia,
                sezione as formazione,
                COUNT(*) as num_bobine,
                SUM(metri_totali) as metri_acquistati,
                SUM(metri_residui) as metri_residui
            FROM parco_cavi
            WHERE id_cantiere = :cantiere_id AND stato_bobina != 'TERMINATA'
            GROUP BY tipologia, sezione
            ORDER BY tipologia, sezione
        """)
        
        bobine_result = db_session.execute(bobine_query, {"cantiere_id": cantiere_id}).fetchall()
        
        print(f"Numero righe bobine: {len(bobine_result)}")
        for i, row in enumerate(bobine_result):
            print(f"  Riga {i+1}: {row}")
            print(f"    Tipologia: {row[0]}")
            print(f"    Formazione: {row[1]}")
            print(f"    Metri acquistati: {row[3]}")
            print(f"    Metri residui: {row[4]}")
        
        # Test 4: Query metri orfani
        print("\n🔍 TEST 4: QUERY METRI ORFANI")
        print("-" * 40)
        
        orfani_query = text("""
            SELECT
                tipologia,
                sezione as formazione,
                COUNT(*) as num_cavi_orfani,
                SUM(metratura_reale) as metri_orfani_totali
            FROM cavi
            WHERE id_cantiere = :cantiere_id
                AND id_bobina = 'BOBINA_VUOTA'
                AND stato_installazione = 'Installato'
                AND (modificato_manualmente IS NULL OR modificato_manualmente != 3)
            GROUP BY tipologia, sezione
            ORDER BY tipologia, sezione
        """)
        
        orfani_result = db_session.execute(orfani_query, {"cantiere_id": cantiere_id}).fetchall()
        
        print(f"Numero righe metri orfani: {len(orfani_result)}")
        for i, row in enumerate(orfani_result):
            print(f"  Riga {i+1}: {row}")
        
        # Test 5: Verifica dati grezzi
        print("\n🔍 TEST 5: VERIFICA DATI GREZZI")
        print("-" * 40)
        
        # Conta totale cavi
        total_cavi = db_session.execute(
            text("SELECT COUNT(*) FROM cavi WHERE id_cantiere = :cantiere_id"),
            {"cantiere_id": cantiere_id}
        ).scalar()
        
        print(f"Totale cavi nel cantiere: {total_cavi}")
        
        # Conta totale bobine
        total_bobine = db_session.execute(
            text("SELECT COUNT(*) FROM parco_cavi WHERE id_cantiere = :cantiere_id"),
            {"cantiere_id": cantiere_id}
        ).scalar()
        
        print(f"Totale bobine nel cantiere: {total_bobine}")
        
        # Verifica filtro modificato_manualmente
        cavi_filtrati = db_session.execute(
            text("SELECT COUNT(*) FROM cavi WHERE id_cantiere = :cantiere_id AND (modificato_manualmente IS NULL OR modificato_manualmente != 3)"),
            {"cantiere_id": cantiere_id}
        ).scalar()
        
        print(f"Cavi dopo filtro modificato_manualmente: {cavi_filtrati}")
        
        # Caso specifico FG16OR16
        print("\n🔍 TEST 6: CASO SPECIFICO FG16OR16")
        print("-" * 40)
        
        fg16_cavi = db_session.execute(
            text("SELECT tipologia, sezione, stato_installazione, metri_teorici FROM cavi WHERE id_cantiere = :cantiere_id AND tipologia = 'FG16OR16'"),
            {"cantiere_id": cantiere_id}
        ).fetchall()
        
        print(f"Cavi FG16OR16: {len(fg16_cavi)}")
        for row in fg16_cavi:
            print(f"  {row}")
        
        fg16_bobine = db_session.execute(
            text("SELECT tipologia, sezione, metri_totali, metri_residui, stato_bobina FROM parco_cavi WHERE id_cantiere = :cantiere_id AND tipologia = 'FG16OR16'"),
            {"cantiere_id": cantiere_id}
        ).fetchall()
        
        print(f"Bobine FG16OR16: {len(fg16_bobine)}")
        for row in fg16_bobine:
            print(f"  {row}")
        
    except Exception as e:
        print(f"❌ Errore durante il test: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'db_session' in locals():
            db_session.close()

if __name__ == "__main__":
    test_sqlalchemy_queries()
