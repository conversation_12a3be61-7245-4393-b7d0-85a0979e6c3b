import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  LinearProgress,
  Tooltip
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon
} from '@mui/icons-material';

const MetricCard = ({
  title,
  value,
  unit = '',
  subtitle,
  gradient,
  icon,
  trend,
  trendValue,
  progress,
  progressColor = '#27ae60',
  onClick,
  highlight = false,
  size = 'medium', // 'small', 'medium', 'large'
  tooltip
}) => {
  
  const getTrendIcon = (trend) => {
    const iconProps = { sx: { fontSize: 16, mr: 0.5 } };
    
    switch (trend) {
      case 'up':
        return <TrendingUpIcon {...iconProps} sx={{ ...iconProps.sx, color: '#27ae60' }} />;
      case 'down':
        return <TrendingDownIcon {...iconProps} sx={{ ...iconProps.sx, color: '#e74c3c' }} />;
      case 'flat':
        return <TrendingFlatIcon {...iconProps} sx={{ ...iconProps.sx, color: '#f39c12' }} />;
      default:
        return null;
    }
  };

  const getTrendColor = (trend) => {
    switch (trend) {
      case 'up': return '#27ae60';
      case 'down': return '#e74c3c';
      case 'flat': return '#f39c12';
      default: return '#666';
    }
  };

  const getSizeStyles = (size) => {
    switch (size) {
      case 'small':
        return {
          padding: 1,
          valueSize: 'h5',
          titleSize: 'body1',
          subtitleSize: 'body2'
        };
      case 'large':
        return {
          padding: 2,
          valueSize: 'h2',
          titleSize: 'h5',
          subtitleSize: 'body1'
        };
      default: // medium
        return {
          padding: 1.5,
          valueSize: 'h3',
          titleSize: 'h6',
          subtitleSize: 'body2'
        };
    }
  };

  const sizeStyles = getSizeStyles(size);

  const cardComponent = (
    <Card
      sx={{
        height: '100%',
        background: gradient || 'white',
        color: gradient ? 'white' : 'inherit',
        transition: 'all 0.3s ease',
        cursor: onClick ? 'pointer' : 'default',
        border: highlight ? '2px solid #3498db' : '1px solid #e0e0e0',
        '&:hover': onClick ? {
          transform: 'translateY(-4px)',
          boxShadow: '0 8px 16px rgba(0,0,0,0.15)'
        } : {}
      }}
      onClick={onClick}
    >
      <CardContent sx={{ textAlign: 'center', p: sizeStyles.padding }}>
        {/* Icon */}
        {icon && (
          <Box sx={{ mb: 1 }}>
            {React.cloneElement(icon, {
              sx: { 
                fontSize: size === 'large' ? 48 : size === 'small' ? 24 : 32,
                opacity: gradient ? 0.9 : 0.7,
                ...icon.props.sx
              }
            })}
          </Box>
        )}

        {/* Value */}
        <Typography 
          variant={sizeStyles.valueSize} 
          sx={{ 
            fontWeight: 700, 
            mb: 0.5,
            opacity: gradient ? 1 : 0.9
          }}
        >
          {value}{unit}
        </Typography>

        {/* Title */}
        <Typography 
          variant={sizeStyles.titleSize} 
          sx={{ 
            opacity: gradient ? 0.9 : 0.8, 
            mb: subtitle || trend || progress !== undefined ? 1 : 0,
            fontWeight: 600
          }}
        >
          {title}
        </Typography>

        {/* Subtitle */}
        {subtitle && (
          <Typography 
            variant={sizeStyles.subtitleSize} 
            sx={{ 
              opacity: gradient ? 0.8 : 0.7,
              mb: trend || progress !== undefined ? 1 : 0
            }}
          >
            {subtitle}
          </Typography>
        )}

        {/* Trend */}
        {trend && trendValue && (
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
            <Chip
              icon={getTrendIcon(trend)}
              label={trendValue}
              size="small"
              sx={{
                bgcolor: gradient ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.05)',
                color: gradient ? 'white' : getTrendColor(trend),
                fontSize: '0.75rem',
                '& .MuiChip-icon': {
                  color: gradient ? 'white' : getTrendColor(trend)
                }
              }}
            />
          </Box>
        )}

        {/* Progress Bar */}
        {progress !== undefined && (
          <Box sx={{ mt: 1 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
              <Typography variant="caption" sx={{ opacity: gradient ? 0.8 : 0.7 }}>
                Progresso
              </Typography>
              <Typography variant="caption" sx={{ fontWeight: 600, opacity: gradient ? 0.9 : 0.8 }}>
                {progress}%
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={progress}
              sx={{
                height: 6,
                borderRadius: 3,
                bgcolor: gradient ? 'rgba(255,255,255,0.3)' : 'rgba(0,0,0,0.1)',
                '& .MuiLinearProgress-bar': {
                  bgcolor: gradient ? 'white' : progressColor,
                  borderRadius: 3
                }
              }}
            />
          </Box>
        )}
      </CardContent>
    </Card>
  );

  // Se c'è un tooltip, avvolgi la card con Tooltip
  return tooltip ? (
    <Tooltip title={tooltip} arrow placement="top">
      {cardComponent}
    </Tooltip>
  ) : cardComponent;
};

export default MetricCard;
