#!/usr/bin/env python3
"""
Script per testare direttamente la funzione generate_boq_data
"""

import sys
import os
import asyncio

# Aggiungi il percorso del backend al PYTHONPATH
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

async def test_generate_boq_direct():
    """Testa direttamente la funzione generate_boq_data."""
    
    print("=== TEST DIRETTO generate_boq_data ===\n")
    
    try:
        from backend.database import get_db
        from backend.api.reports_simple import generate_boq_data
        
        # Ottieni una sessione del database
        db_session = next(get_db())
        cantiere_id = 1
        
        print(f"✅ Sessione database ottenuta")
        print(f"🔍 Chiamando generate_boq_data per cantiere {cantiere_id}")
        
        # Chiama la funzione direttamente
        result = await generate_boq_data(cantiere_id, db_session)
        
        print(f"✅ Funzione eseguita con successo!")
        print(f"Nome cantiere: {result.get('nome_cantiere', 'N/A')}")
        
        distinta = result.get('distinta_materiali', [])
        print(f"Numero categorie: {len(distinta)}")
        
        if len(distinta) == 0:
            print("❌ NESSUNA CATEGORIA TROVATA!")
            print("Questo indica un problema nella logica della funzione.")
        else:
            print(f"✅ Trovate {len(distinta)} categorie:")
            
            # Cerca il caso specifico FG16OR16 1X240MM2
            caso_trovato = False
            
            for i, item in enumerate(distinta):
                tipologia = item.get('tipologia', 'N/A')
                formazione = item.get('formazione', 'N/A')
                metri_acquistati = item.get('metri_acquistati', 0)
                metri_residui = item.get('metri_residui', 0)
                metri_da_posare = item.get('metri_da_posare', 0)
                metri_mancanti = item.get('metri_mancanti', 0)
                necessita_acquisto = item.get('necessita_acquisto', True)
                
                print(f"  {i+1}. {tipologia} | {formazione}")
                print(f"     Metri acquistati: {metri_acquistati}")
                print(f"     Metri residui: {metri_residui}")
                print(f"     Metri da posare: {metri_da_posare}")
                print(f"     Metri mancanti: {metri_mancanti}")
                print(f"     Necessita acquisto: {'Sì' if necessita_acquisto else 'No'}")
                
                # Caso specifico
                if tipologia == "FG16OR16" and "240MM2" in formazione:
                    caso_trovato = True
                    print(f"     *** CASO SPECIFICO TROVATO! ***")
                    print(f"     🎯 Risultato funzione:")
                    print(f"       - Metri acquistati: {metri_acquistati}m")
                    print(f"       - Metri residui: {metri_residui}m")
                    print(f"       - Metri da posare: {metri_da_posare}m")
                    print(f"       - Metri mancanti: {metri_mancanti}m")
                    print(f"       - Necessita acquisto: {'Sì' if necessita_acquisto else 'No'}")
                    
                    if metri_residui > 0 and not necessita_acquisto:
                        print(f"       🎉 FUNZIONE CORRETTA: Bobina riconosciuta!")
                    else:
                        print(f"       ❌ FUNZIONE ERRATA: Bobina non riconosciuta!")
                print()
            
            if not caso_trovato:
                print("❌ CASO SPECIFICO FG16OR16 1X240MM2 NON TROVATO!")
        
        # Riepilogo
        riepilogo = result.get('riepilogo', {})
        print(f"📊 RIEPILOGO:")
        print(f"  Totale categorie: {riepilogo.get('totale_categorie', 0)}")
        print(f"  Totale metri acquistati: {riepilogo.get('totale_metri_acquistati', 0)}m")
        print(f"  Totale metri residui: {riepilogo.get('totale_metri_residui', 0)}m")
        print(f"  Totale metri mancanti: {riepilogo.get('totale_metri_mancanti', 0)}m")
        print(f"  Categorie che necessitano acquisto: {riepilogo.get('categorie_necessitano_acquisto', 0)}")
        
    except Exception as e:
        print(f"❌ Errore durante il test: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'db_session' in locals():
            db_session.close()

if __name__ == "__main__":
    asyncio.run(test_generate_boq_direct())
