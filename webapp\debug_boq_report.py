#!/usr/bin/env python3
"""
Script per diagnosticare perché la bobina non appare nel report BOQ
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import sys
import os

# Aggiungi il percorso del backend al PYTHONPATH
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

# Configurazione database
DB_HOST = "localhost"
DB_PORT = "5432"
DB_NAME = "cantieri"
DB_USER = "postgres"
DB_PASSWORD = "Taranto"

def connect_to_db():
    """Connessione al database PostgreSQL."""
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD,
            cursor_factory=RealDictCursor
        )
        print(f"✅ Connesso al database {DB_NAME}")
        return conn
    except Exception as e:
        print(f"❌ Errore durante la connessione al database: {e}")
        return None

def debug_boq_queries():
    """Debug delle query del report BOQ per capire il problema."""
    
    print("=== DEBUG REPORT BOQ ===\n")
    
    conn = connect_to_db()
    if not conn:
        return
    
    try:
        with conn.cursor() as cur:
            cantiere_id = 1
            
            print("🔍 STEP 1: VERIFICA DATI CAVI")
            print("-" * 50)
            
            # Query esatta del report per i cavi
            cavi_query = """
                SELECT
                    tipologia,
                    sezione as formazione,
                    COUNT(*) as num_cavi,
                    COUNT(CASE WHEN stato_installazione != 'Installato' THEN 1 END) as num_cavi_rimanenti,
                    SUM(metri_teorici) as metri_teorici_totali,
                    SUM(CASE WHEN stato_installazione = 'Installato' THEN metratura_reale ELSE 0 END) as metri_reali_posati,
                    SUM(CASE WHEN stato_installazione != 'Installato' THEN metri_teorici ELSE 0 END) as metri_da_posare
                FROM cavi
                WHERE id_cantiere = %s AND (modificato_manualmente IS NULL OR modificato_manualmente != 3)
                GROUP BY tipologia, sezione
                ORDER BY tipologia, sezione
            """
            
            cur.execute(cavi_query, (cantiere_id,))
            cavi_results = cur.fetchall()
            
            print(f"Trovati {len(cavi_results)} gruppi di cavi:")
            for cavo in cavi_results:
                print(f"  {cavo['tipologia']} | {cavo['formazione']} | {cavo['num_cavi']} cavi | {cavo['metri_da_posare']}m da posare")
            
            print("\n🔍 STEP 2: VERIFICA DATI BOBINE")
            print("-" * 50)
            
            # Query esatta del report per le bobine
            bobine_query = """
                SELECT
                    tipologia,
                    sezione as formazione,
                    COUNT(*) as num_bobine,
                    SUM(metri_totali) as metri_acquistati,
                    SUM(metri_residui) as metri_residui
                FROM parco_cavi
                WHERE id_cantiere = %s AND stato_bobina != 'TERMINATA'
                GROUP BY tipologia, sezione
                ORDER BY tipologia, sezione
            """
            
            cur.execute(bobine_query, (cantiere_id,))
            bobine_results = cur.fetchall()
            
            print(f"Trovati {len(bobine_results)} gruppi di bobine:")
            for bobina in bobine_results:
                print(f"  {bobina['tipologia']} | {bobina['formazione']} | {bobina['num_bobine']} bobine | {bobina['metri_acquistati']}m acquistati | {bobina['metri_residui']}m residui")
            
            print("\n🔍 STEP 3: VERIFICA CONDIZIONI FILTRO BOBINE")
            print("-" * 50)
            
            # Verifica tutte le bobine senza filtri
            cur.execute("""
                SELECT id_bobina, tipologia, sezione, metri_totali, metri_residui, stato_bobina, id_cantiere
                FROM parco_cavi
                ORDER BY id_cantiere, tipologia, sezione
            """)
            
            all_bobine = cur.fetchall()
            print(f"TUTTE le bobine nel database ({len(all_bobine)}):")
            for bobina in all_bobine:
                print(f"  ID: {bobina['id_bobina']} | {bobina['tipologia']} | {bobina['sezione']} | {bobina['metri_residui']}m | Stato: {bobina['stato_bobina']} | Cantiere: {bobina['id_cantiere']}")
            
            print("\n🔍 STEP 4: TEST FILTRI BOBINE")
            print("-" * 50)
            
            # Test vari filtri
            filtri_test = [
                ("Nessun filtro", "SELECT * FROM parco_cavi WHERE id_cantiere = %s"),
                ("Stato != TERMINATA", "SELECT * FROM parco_cavi WHERE id_cantiere = %s AND stato_bobina != 'TERMINATA'"),
                ("Stato != TERMINATA (case insensitive)", "SELECT * FROM parco_cavi WHERE id_cantiere = %s AND UPPER(stato_bobina) != 'TERMINATA'"),
                ("Metri residui > 0", "SELECT * FROM parco_cavi WHERE id_cantiere = %s AND metri_residui > 0"),
                ("Combinato", "SELECT * FROM parco_cavi WHERE id_cantiere = %s AND stato_bobina != 'TERMINATA' AND metri_residui >= 0")
            ]
            
            for nome_filtro, query in filtri_test:
                cur.execute(query, (cantiere_id,))
                risultati = cur.fetchall()
                print(f"  {nome_filtro}: {len(risultati)} bobine")
                for bobina in risultati:
                    print(f"    {bobina['id_bobina']} | {bobina['tipologia']} | {bobina['sezione']} | {bobina['stato_bobina']}")
            
            print("\n🔍 STEP 5: SIMULAZIONE MATCHING COMPLETO")
            print("-" * 50)
            
            # Simula il matching completo come nel report
            bobine_lookup = {}
            for bobina in bobine_results:
                tipologia_norm = (bobina['tipologia'] or "").upper().strip()
                formazione_norm = (bobina['formazione'] or "").upper().strip()
                key = f"{tipologia_norm}_{formazione_norm}"
                bobine_lookup[key] = {
                    "num_bobine": int(bobina['num_bobine']),
                    "metri_acquistati": round(float(bobina['metri_acquistati'] or 0), 2),
                    "metri_residui": round(float(bobina['metri_residui'] or 0), 2)
                }
            
            print(f"Bobine lookup dictionary: {list(bobine_lookup.keys())}")
            
            print("\nMatching cavi -> bobine:")
            for cavo in cavi_results:
                tipologia_norm = (cavo['tipologia'] or "").upper().strip()
                formazione_norm = (cavo['formazione'] or "").upper().strip()
                key = f"{tipologia_norm}_{formazione_norm}"
                
                bobina_info = bobine_lookup.get(key, {"num_bobine": 0, "metri_acquistati": 0, "metri_residui": 0})
                
                print(f"  Cavo: {cavo['tipologia']} | {cavo['formazione']}")
                print(f"    Key normalizzata: '{key}'")
                print(f"    Bobina trovata: {bobina_info}")
                print(f"    Match: {'✅' if bobina_info['metri_residui'] > 0 else '❌'}")
                
                # Caso specifico
                if "FG16OR16" in tipologia_norm and "240MM2" in formazione_norm:
                    print(f"    *** CASO SPECIFICO FG16OR16 240MM2 ***")
                    print(f"    Cavo originale: '{cavo['tipologia']}' | '{cavo['formazione']}'")
                    print(f"    Normalizzato: '{tipologia_norm}' | '{formazione_norm}'")
                    print(f"    Metri da posare: {cavo['metri_da_posare']}")
                    print(f"    Metri residui bobina: {bobina_info['metri_residui']}")
                    metri_mancanti = max(0, float(cavo['metri_da_posare']) - bobina_info['metri_residui'])
                    print(f"    Metri mancanti: {metri_mancanti}")
                    print(f"    Necessita acquisto: {'Sì' if metri_mancanti > 0 else 'No'}")
                print()
                
    except Exception as e:
        print(f"❌ Errore durante il debug: {e}")
        import traceback
        traceback.print_exc()
    finally:
        conn.close()

if __name__ == "__main__":
    debug_boq_queries()
