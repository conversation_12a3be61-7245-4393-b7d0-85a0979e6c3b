#!/usr/bin/env python3
"""
Script per controllare le bobine nel database e verificare il calcolo BOQ
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import sys
import os

# Configurazione database
DB_HOST = "localhost"
DB_PORT = "5432"
DB_NAME = "cantieri"
DB_USER = "postgres"
DB_PASSWORD = "Taranto"

def connect_to_db():
    """Connessione al database PostgreSQL."""
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD,
            cursor_factory=RealDictCursor
        )
        print(f"✅ Connesso al database {DB_NAME}")
        return conn
    except Exception as e:
        print(f"❌ Errore durante la connessione al database: {e}")
        sys.exit(1)

def check_bobine(cantiere_id=1):
    """<PERSON>la le bobine nel parco cavi."""
    conn = connect_to_db()
    
    try:
        with conn.cursor() as cur:
            # Controlla tutte le bobine del cantiere
            cur.execute("""
                SELECT 
                    id_bobina, 
                    tipologia, 
                    sezione, 
                    metri_totali, 
                    metri_residui, 
                    stato_bobina,
                    id_cantiere
                FROM parco_cavi 
                WHERE id_cantiere = %s OR id_cantiere IS NULL
                ORDER BY id_bobina
            """, (cantiere_id,))
            
            bobine = cur.fetchall()
            
            print(f"\n📦 BOBINE NEL CANTIERE {cantiere_id}:")
            print("=" * 80)
            
            if not bobine:
                print("❌ Nessuna bobina trovata!")
                return
            
            for bobina in bobine:
                print(f"ID: {bobina['id_bobina']}")
                print(f"  Tipologia: {bobina['tipologia']}")
                print(f"  Sezione: {bobina['sezione']}")
                print(f"  Metri totali: {bobina['metri_totali']}")
                print(f"  Metri residui: {bobina['metri_residui']}")
                print(f"  Stato: {bobina['stato_bobina']}")
                print(f"  Cantiere: {bobina['id_cantiere']}")
                print("-" * 40)
            
            # Controlla anche i cavi installati
            print(f"\n🔌 CAVI INSTALLATI NEL CANTIERE {cantiere_id}:")
            print("=" * 80)
            
            cur.execute("""
                SELECT 
                    tipologia, 
                    sezione, 
                    stato_installazione,
                    id_bobina,
                    metratura_reale,
                    COUNT(*) as num_cavi
                FROM cavi 
                WHERE id_cantiere = %s 
                GROUP BY tipologia, sezione, stato_installazione, id_bobina, metratura_reale
                ORDER BY tipologia, sezione
            """, (cantiere_id,))
            
            cavi = cur.fetchall()
            
            for cavo in cavi:
                print(f"Tipologia: {cavo['tipologia']}")
                print(f"  Sezione: {cavo['sezione']}")
                print(f"  Stato: {cavo['stato_installazione']}")
                print(f"  Bobina: {cavo['id_bobina']}")
                print(f"  Metri reali: {cavo['metratura_reale']}")
                print(f"  Numero cavi: {cavo['num_cavi']}")
                print("-" * 40)
                
    except Exception as e:
        print(f"❌ Errore durante la query: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    cantiere_id = int(sys.argv[1]) if len(sys.argv) > 1 else 1
    check_bobine(cantiere_id)
