import axios from 'axios';
import config from '../config';
import axiosInstance from './axiosConfig';

const API_URL = config.API_URL;

const reportService = {
  // Ottiene il report di avanzamento
  getProgressReport: async (cantiereId, formato = 'video') => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/progress?formato=${formato}`);
      return response.data;
    } catch (error) {
      console.error('Get progress report error:', error);
      throw error.response ? error.response.data : error;
    }
  },

  // Ottiene la distinta materiali (Bill of Quantities)
  getBillOfQuantities: async (cantiereId, formato = 'video') => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/boq?formato=${formato}`);
      return response.data;
    } catch (error) {
      console.error('Get bill of quantities error:', error);
      throw error.response ? error.response.data : error;
    }
  },



  // Ottiene il report di posa per periodo
  getPosaPerPeriodoReport: async (cantiereId, dataInizio, dataFine, formato = 'video') => {
    try {
      // Assicurati che cantiereId sia un numero
      const cantiereIdNum = parseInt(cantiereId, 10);
      if (isNaN(cantiereIdNum)) {
        throw new Error(`ID cantiere non valido: ${cantiereId}`);
      }

      const response = await axiosInstance.get(
        `/reports/${cantiereIdNum}/posa-periodo?data_inizio=${dataInizio}&data_fine=${dataFine}&formato=${formato}`
      );
      return response.data;
    } catch (error) {
      console.error('Get posa per periodo report error:', error);
      throw error.response ? error.response.data : error;
    }
  },


};

export default reportService;
