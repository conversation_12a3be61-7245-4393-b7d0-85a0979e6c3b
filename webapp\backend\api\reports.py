"""
API endpoints per la gestione dei report del sistema CMS.
Adatta la logica dei report dalla CLI per funzionare con FastAPI e PostgreSQL.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional
import logging
import os
import tempfile
from datetime import datetime, timedelta
import json

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
from modules.database_pg import Database
from models import User
from auth import get_current_active_user
from reports_helpers import (
    _generate_progress_report_data,
    _generate_progress_report_file,
    _generate_boq_report_data,
    _generate_boq_report_file,
    _generate_posa_periodo_report_data,
    _generate_posa_periodo_report_file
)

router = APIRouter()

# Directory per i report temporanei
REPORTS_DIR = os.path.join(os.path.dirname(__file__), '..', 'static', 'reports')
os.makedirs(REPORTS_DIR, exist_ok=True)

@router.get("/{cantiere_id}/progress")
async def get_progress_report(
    cantiere_id: int,
    formato: str = Query(default="pdf", regex="^(pdf|excel|video)$"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Genera un report dettagliato sull'avanzamento dei lavori.

    Args:
        cantiere_id: ID del cantiere
        formato: Formato del report (pdf, excel, video)

    Returns:
        Dict con il contenuto del report o URL del file
    """
    try:
        db = Database()

        # Verifica che il cantiere esista e l'utente abbia accesso
        cantiere = db.execute_query(
            "SELECT nome FROM cantieri WHERE id_cantiere = %s",
            (cantiere_id,), fetch_one=True
        )

        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        if formato == "video":
            # Genera report per visualizzazione a schermo
            report_data = await _generate_progress_report_data(cantiere_id)
            return {
                "success": True,
                "content": report_data,
                "formato": "video"
            }
        else:
            # Genera file PDF o Excel
            file_path = await _generate_progress_report_file(cantiere_id, formato)
            if not file_path:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Errore durante la generazione del report"
                )

            # Restituisci URL per il download
            filename = os.path.basename(file_path)
            file_url = f"/static/reports/{filename}"

            return {
                "success": True,
                "file_url": file_url,
                "filename": filename,
                "formato": formato
            }

    except Exception as e:
        logging.error(f"Errore nella generazione del progress report: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante la generazione del report: {str(e)}"
        )

@router.get("/{cantiere_id}/boq")
async def get_bill_of_quantities(
    cantiere_id: int,
    formato: str = Query(default="pdf", regex="^(pdf|excel|video)$"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Genera un report sulla distinta materiali (Bill of Quantities).
    """
    try:
        db = Database()

        cantiere = db.execute_query(
            "SELECT nome FROM cantieri WHERE id_cantiere = %s",
            (cantiere_id,), fetch_one=True
        )

        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        if formato == "video":
            report_data = await _generate_boq_report_data(cantiere_id)
            return {
                "success": True,
                "content": report_data,
                "formato": "video"
            }
        else:
            file_path = await _generate_boq_report_file(cantiere_id, formato)
            if not file_path:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Errore durante la generazione del report"
                )

            filename = os.path.basename(file_path)
            file_url = f"/static/reports/{filename}"

            return {
                "success": True,
                "file_url": file_url,
                "filename": filename,
                "formato": formato
            }

    except Exception as e:
        logging.error(f"Errore nella generazione del Bill of Quantities: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante la generazione del report: {str(e)}"
        )





@router.get("/{cantiere_id}/posa-periodo")
async def get_posa_periodo_report(
    cantiere_id: int,
    formato: str = Query(default="pdf", regex="^(pdf|excel|video)$"),
    data_inizio: Optional[str] = Query(None, description="Data inizio periodo (YYYY-MM-DD)"),
    data_fine: Optional[str] = Query(None, description="Data fine periodo (YYYY-MM-DD)"),
    current_user: User = Depends(get_current_active_user)
):
    """
    Genera un report sulla posa per periodo.
    """
    try:
        db = Database()

        cantiere = db.execute_query(
            "SELECT nome FROM cantieri WHERE id_cantiere = %s",
            (cantiere_id,), fetch_one=True
        )

        if not cantiere:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cantiere con ID {cantiere_id} non trovato"
            )

        if formato == "video":
            report_data = await _generate_posa_periodo_report_data(cantiere_id, data_inizio, data_fine)
            return {
                "success": True,
                "content": report_data,
                "formato": "video"
            }
        else:
            file_path = await _generate_posa_periodo_report_file(cantiere_id, data_inizio, data_fine, formato)
            if not file_path:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Errore durante la generazione del report"
                )

            filename = os.path.basename(file_path)
            file_url = f"/static/reports/{filename}"

            return {
                "success": True,
                "file_url": file_url,
                "filename": filename,
                "formato": formato
            }

    except Exception as e:
        logging.error(f"Errore nella generazione del report posa periodo: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore durante la generazione del report: {str(e)}"
        )



# Fine degli endpoint API
