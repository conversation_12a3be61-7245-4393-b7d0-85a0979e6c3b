{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\charts\\\\BoqChart.js\";\nimport React from 'react';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, XAxis, <PERSON>A<PERSON>s, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer, <PERSON><PERSON><PERSON>, Pie, Cell, ComposedChart, Line, LineChart } from 'recharts';\nimport { Box, Typography, Grid, Paper, Chip } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst COLORS = {\n  primary: '#1976d2',\n  secondary: '#dc004e',\n  success: '#2e7d32',\n  warning: '#ed6c02',\n  info: '#0288d1',\n  error: '#d32f2f',\n  purple: '#9c27b0',\n  teal: '#00695c'\n};\nconst BoqChart = ({\n  data\n}) => {\n  var _data$distinta_materi, _data$distinta_materi2, _data$riepilogo, _data$riepilogo$total, _data$riepilogo2, _data$riepilogo2$tota, _data$riepilogo3, _data$riepilogo3$tota, _data$riepilogo4, _data$riepilogo4$perc;\n  if (!data) return null;\n\n  // Prepara dati per grafici dalla nuova struttura distinta_materiali\n  const caviData = ((_data$distinta_materi = data.distinta_materiali) === null || _data$distinta_materi === void 0 ? void 0 : _data$distinta_materi.map((cavo, index) => {\n    var _cavo$tipologia;\n    return {\n      ...cavo,\n      // Mappa i nuovi campi ai vecchi per compatibilità\n      metri_teorici: cavo.metri_teorici_totali,\n      metri_reali: cavo.metri_reali_posati,\n      tipologia_short: ((_cavo$tipologia = cavo.tipologia) === null || _cavo$tipologia === void 0 ? void 0 : _cavo$tipologia.length) > 8 ? cavo.tipologia.substring(0, 8) + '...' : cavo.tipologia,\n      color: Object.values(COLORS)[index % Object.values(COLORS).length],\n      deficit: Math.max(0, cavo.metri_da_posare - (cavo.metri_reali_posati || 0)),\n      surplus: Math.max(0, (cavo.metri_reali_posati || 0) - cavo.metri_da_posare)\n    };\n  })) || [];\n\n  // Prepara dati per grafici bobine (ora inclusi nella distinta_materiali)\n  const bobineData = ((_data$distinta_materi2 = data.distinta_materiali) === null || _data$distinta_materi2 === void 0 ? void 0 : _data$distinta_materi2.filter(item => item.num_bobine > 0).map((bobina, index) => {\n    var _bobina$tipologia;\n    return {\n      tipologia: bobina.tipologia,\n      sezione: bobina.sezione,\n      num_bobine: bobina.num_bobine,\n      metri_disponibili: bobina.metri_disponibili,\n      tipologia_short: ((_bobina$tipologia = bobina.tipologia) === null || _bobina$tipologia === void 0 ? void 0 : _bobina$tipologia.length) > 8 ? bobina.tipologia.substring(0, 8) + '...' : bobina.tipologia,\n      color: Object.values(COLORS)[index % Object.values(COLORS).length]\n    };\n  })) || [];\n\n  // Calcola totali per grafici a torta\n  const totaliCavi = caviData.reduce((acc, cavo) => {\n    acc.teorici += cavo.metri_teorici || 0;\n    acc.reali += cavo.metri_reali || 0;\n    acc.da_posare += cavo.metri_da_posare || 0;\n    return acc;\n  }, {\n    teorici: 0,\n    reali: 0,\n    da_posare: 0\n  });\n  const totaliData = [{\n    name: 'Metri Teorici',\n    value: totaliCavi.teorici,\n    color: COLORS.primary\n  }, {\n    name: 'Metri Reali',\n    value: totaliCavi.reali,\n    color: COLORS.success\n  }, {\n    name: 'Metri da Posare',\n    value: totaliCavi.da_posare,\n    color: COLORS.warning\n  }];\n\n  // Analisi deficit/surplus\n  const analisiData = caviData.map(cavo => ({\n    tipologia: cavo.tipologia_short,\n    tipologia_full: cavo.tipologia,\n    deficit: cavo.deficit,\n    surplus: cavo.surplus,\n    necessita_acquisto: cavo.deficit > 0\n  }));\n  const CustomTooltip = ({\n    active,\n    payload,\n    label\n  }) => {\n    if (active && payload && payload.length) {\n      return /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1,\n          border: '1px solid #ccc'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: `${label}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), payload.map((entry, index) => /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          style: {\n            color: entry.color\n          },\n          children: `${entry.name}: ${typeof entry.value === 'number' ? entry.value.toFixed(2) : entry.value}`\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  const renderCustomizedLabel = ({\n    cx,\n    cy,\n    midAngle,\n    innerRadius,\n    outerRadius,\n    percent\n  }) => {\n    if (percent < 0.05) return null;\n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n    return /*#__PURE__*/_jsxDEV(\"text\", {\n      x: x,\n      y: y,\n      fill: \"white\",\n      textAnchor: x > cx ? 'start' : 'end',\n      dominantBaseline: \"central\",\n      fontSize: \"12\",\n      fontWeight: \"bold\",\n      children: `${(percent * 100).toFixed(0)}%`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      mt: 3\n    },\n    children: [data.metri_orfani && data.metri_orfani.metri_orfani_totali > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      sx: {\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 0,\n            border: '2px solid #ff9800',\n            borderRadius: 1,\n            bgcolor: '#fff3e0'\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 600,\n                color: '#e65100',\n                mb: 1\n              },\n              children: \"\\u26A0\\uFE0F METRI POSATI SENZA BOBINA ASSOCIATA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                color: '#bf360c',\n                fontWeight: 500,\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [data.metri_orfani.metri_orfani_totali, \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 19\n              }, this), \" posati con BOBINA_VUOTA (\", data.metri_orfani.num_cavi_orfani, \" cavi)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: '#5d4037'\n              },\n              children: [\"Questi metri sono stati installati ma non sono tracciati nel parco bobine.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Azione richiesta:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this), \" Associare a bobine esistenti o registrare nuove bobine per tracciabilit\\xE0 completa.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 0,\n            border: '1px solid #e0e0e0',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              bgcolor: '#f8f9fa',\n              p: 2,\n              borderBottom: '1px solid #e0e0e0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 600,\n                color: '#2c3e50'\n              },\n              children: \"\\uD83D\\uDCCB Bill of Quantities - Distinta Materiali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: '#666',\n                mt: 0.5\n              },\n              children: \"Riepilogo completo dei materiali per tipologia di cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              overflow: 'auto'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              style: {\n                width: '100%',\n                borderCollapse: 'collapse'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  style: {\n                    backgroundColor: '#f8f9fa'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'left',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Tipologia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Conduttori\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Sezione\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"N\\xB0 Cavi\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Metri Teorici\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Metri Posati\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Metri Rimanenti\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Metri Disponibili\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Metri Mancanti\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Necessita Acquisto\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"% Completamento\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0'\n                    },\n                    children: \"Stato\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: caviData.map((cavo, index) => {\n                  const percentuale = cavo.metri_teorici > 0 ? (cavo.metri_reali || 0) / cavo.metri_teorici * 100 : 0;\n                  const isCompleto = percentuale >= 100;\n                  const isInCorso = percentuale > 0 && percentuale < 100;\n                  return /*#__PURE__*/_jsxDEV(\"tr\", {\n                    style: {\n                      backgroundColor: index % 2 === 0 ? '#ffffff' : '#fafafa'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        fontSize: '13px',\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0',\n                        fontWeight: 500\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: 1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            width: '4px',\n                            height: '20px',\n                            backgroundColor: cavo.color,\n                            borderRadius: '2px'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 293,\n                          columnNumber: 29\n                        }, this), cavo.tipologia]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 292,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 285,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'center',\n                        fontSize: '13px',\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: cavo.conduttori || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 302,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'center',\n                        fontSize: '13px',\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: cavo.sezione || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 309,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'center',\n                        fontSize: '13px',\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: cavo.num_cavi\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 316,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'right',\n                        fontSize: '13px',\n                        fontWeight: 600,\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: [(cavo.metri_teorici || 0).toFixed(1), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 323,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'right',\n                        fontSize: '13px',\n                        fontWeight: 600,\n                        color: COLORS.success,\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: [(cavo.metri_reali || 0).toFixed(1), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'right',\n                        fontSize: '13px',\n                        fontWeight: 600,\n                        color: COLORS.warning,\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: [(cavo.metri_da_posare || 0).toFixed(1), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 340,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'right',\n                        fontSize: '13px',\n                        fontWeight: 600,\n                        color: COLORS.info,\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: [(cavo.metri_disponibili || 0).toFixed(1), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 349,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'right',\n                        fontSize: '13px',\n                        fontWeight: 600,\n                        color: cavo.metri_mancanti > 0 ? COLORS.error : COLORS.success,\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: [(cavo.metri_mancanti || 0).toFixed(1), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 358,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'center',\n                        fontSize: '13px',\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'inline-flex',\n                          alignItems: 'center',\n                          gap: 0.5,\n                          px: 1,\n                          py: 0.5,\n                          borderRadius: '12px',\n                          fontSize: '11px',\n                          fontWeight: 600,\n                          backgroundColor: cavo.necessita_acquisto ? '#ffebee' : '#e8f5e8',\n                          color: cavo.necessita_acquisto ? '#c62828' : '#2e7d32'\n                        },\n                        children: cavo.necessita_acquisto ? '🛒 Sì' : '✅ No'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 374,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 367,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'right',\n                        fontSize: '13px',\n                        fontWeight: 600,\n                        color: isCompleto ? COLORS.success : isInCorso ? COLORS.warning : COLORS.secondary,\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: [percentuale.toFixed(1), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 389,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'center',\n                        fontSize: '13px',\n                        borderBottom: '1px solid #f0f0f0'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'inline-flex',\n                          alignItems: 'center',\n                          gap: 0.5,\n                          px: 1,\n                          py: 0.5,\n                          borderRadius: '12px',\n                          fontSize: '11px',\n                          fontWeight: 600,\n                          backgroundColor: isCompleto ? '#e8f5e8' : isInCorso ? '#fff3cd' : '#f8f9fa',\n                          color: isCompleto ? '#2e7d32' : isInCorso ? '#856404' : '#6c757d'\n                        },\n                        children: isCompleto ? '✅ Completato' : isInCorso ? '🔄 In Corso' : '⏳ Da Iniziare'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 404,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 398,\n                      columnNumber: 25\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              bgcolor: '#f8f9fa',\n              p: 2,\n              borderTop: '1px solid #e0e0e0'\n            },\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 3,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 3,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    sx: {\n                      color: COLORS.primary,\n                      fontWeight: 600\n                    },\n                    children: [((_data$riepilogo = data.riepilogo) === null || _data$riepilogo === void 0 ? void 0 : (_data$riepilogo$total = _data$riepilogo.totale_metri_teorici) === null || _data$riepilogo$total === void 0 ? void 0 : _data$riepilogo$total.toFixed(1)) || totaliCavi.teorici.toFixed(1), \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: \"Metri Teorici Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 438,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 3,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    sx: {\n                      color: COLORS.success,\n                      fontWeight: 600\n                    },\n                    children: [((_data$riepilogo2 = data.riepilogo) === null || _data$riepilogo2 === void 0 ? void 0 : (_data$riepilogo2$tota = _data$riepilogo2.totale_metri_posati) === null || _data$riepilogo2$tota === void 0 ? void 0 : _data$riepilogo2$tota.toFixed(1)) || totaliCavi.reali.toFixed(1), \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: \"Metri Posati Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 3,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    sx: {\n                      color: COLORS.warning,\n                      fontWeight: 600\n                    },\n                    children: [((_data$riepilogo3 = data.riepilogo) === null || _data$riepilogo3 === void 0 ? void 0 : (_data$riepilogo3$tota = _data$riepilogo3.totale_metri_da_posare) === null || _data$riepilogo3$tota === void 0 ? void 0 : _data$riepilogo3$tota.toFixed(1)) || totaliCavi.da_posare.toFixed(1), \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: \"Metri Rimanenti\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 3,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    sx: {\n                      color: COLORS.primary,\n                      fontWeight: 600\n                    },\n                    children: [((_data$riepilogo4 = data.riepilogo) === null || _data$riepilogo4 === void 0 ? void 0 : (_data$riepilogo4$perc = _data$riepilogo4.percentuale_completamento) === null || _data$riepilogo4$perc === void 0 ? void 0 : _data$riepilogo4$perc.toFixed(1)) || (totaliCavi.teorici > 0 ? (totaliCavi.reali / totaliCavi.teorici * 100).toFixed(1) : 0), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 465,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: \"Completamento Totale\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 5\n  }, this);\n};\n_c = BoqChart;\nexport default BoqChart;\nvar _c;\n$RefreshReg$(_c, \"BoqChart\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "ComposedChart", "Line", "Line<PERSON>hart", "Box", "Typography", "Grid", "Paper", "Chip", "jsxDEV", "_jsxDEV", "COLORS", "primary", "secondary", "success", "warning", "info", "error", "purple", "teal", "<PERSON><PERSON><PERSON><PERSON>", "data", "_data$distinta_materi", "_data$distinta_materi2", "_data$riepilogo", "_data$riepilogo$total", "_data$riepilogo2", "_data$riepilogo2$tota", "_data$riepilogo3", "_data$riepilogo3$tota", "_data$riepilogo4", "_data$riepilogo4$perc", "caviData", "distinta_materiali", "map", "cavo", "index", "_cavo$tipologia", "metri_te<PERSON>ci", "metri_teorici_totali", "metri_reali", "metri_reali_posati", "tipologia_short", "tipologia", "length", "substring", "color", "Object", "values", "deficit", "Math", "max", "metri_da_posare", "surplus", "bobine<PERSON><PERSON>", "filter", "item", "num_bobine", "bobina", "_bobina$tipologia", "sezione", "metri_disponibili", "totaliCavi", "reduce", "acc", "<PERSON><PERSON><PERSON>", "reali", "da_posare", "totaliData", "name", "value", "analisiData", "tipologia_full", "necessita_acquisto", "CustomTooltip", "active", "payload", "label", "sx", "p", "border", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "entry", "style", "toFixed", "renderCustomizedLabel", "cx", "cy", "midAngle", "innerRadius", "outerRadius", "percent", "RADIAN", "PI", "radius", "x", "cos", "y", "sin", "fill", "textAnchor", "dominantBaseline", "fontSize", "fontWeight", "mt", "<PERSON><PERSON>_<PERSON><PERSON>i", "metri_orfani_totali", "container", "spacing", "mb", "xs", "borderRadius", "bgcolor", "num_cavi_orfani", "borderBottom", "overflow", "width", "borderCollapse", "backgroundColor", "padding", "textAlign", "borderRight", "percentuale", "isCompleto", "isInCorso", "display", "alignItems", "gap", "height", "conduttori", "num_cavi", "<PERSON><PERSON>_man<PERSON>ti", "px", "py", "borderTop", "sm", "riepilogo", "totale_metri_teorici", "totale_metri_posati", "totale_metri_da_posare", "percentuale_completamento", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/charts/BoqChart.js"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  Bar,\n  XAxis,\n  <PERSON>A<PERSON><PERSON>,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  ResponsiveContainer,\n  <PERSON><PERSON>hart,\n  Pie,\n  Cell,\n  ComposedChart,\n  Line,\n  LineChart\n} from 'recharts';\nimport { Box, Typography, Grid, Paper, Chip } from '@mui/material';\n\nconst COLORS = {\n  primary: '#1976d2',\n  secondary: '#dc004e',\n  success: '#2e7d32',\n  warning: '#ed6c02',\n  info: '#0288d1',\n  error: '#d32f2f',\n  purple: '#9c27b0',\n  teal: '#00695c'\n};\n\nconst BoqChart = ({ data }) => {\n  if (!data) return null;\n\n  // Prepara dati per grafici dalla nuova struttura distinta_materiali\n  const caviData = data.distinta_materiali?.map((cavo, index) => ({\n    ...cavo,\n    // Mappa i nuovi campi ai vecchi per compatibilità\n    metri_teorici: cavo.metri_teorici_totali,\n    metri_reali: cavo.metri_reali_posati,\n    tipologia_short: cavo.tipologia?.length > 8 ? cavo.tipologia.substring(0, 8) + '...' : cavo.tipologia,\n    color: Object.values(COLORS)[index % Object.values(COLORS).length],\n    deficit: Math.max(0, cavo.metri_da_posare - (cavo.metri_reali_posati || 0)),\n    surplus: Math.max(0, (cavo.metri_reali_posati || 0) - cavo.metri_da_posare)\n  })) || [];\n\n  // Prepara dati per grafici bobine (ora inclusi nella distinta_materiali)\n  const bobineData = data.distinta_materiali?.filter(item => item.num_bobine > 0).map((bobina, index) => ({\n    tipologia: bobina.tipologia,\n    sezione: bobina.sezione,\n    num_bobine: bobina.num_bobine,\n    metri_disponibili: bobina.metri_disponibili,\n    tipologia_short: bobina.tipologia?.length > 8 ? bobina.tipologia.substring(0, 8) + '...' : bobina.tipologia,\n    color: Object.values(COLORS)[index % Object.values(COLORS).length]\n  })) || [];\n\n  // Calcola totali per grafici a torta\n  const totaliCavi = caviData.reduce((acc, cavo) => {\n    acc.teorici += cavo.metri_teorici || 0;\n    acc.reali += cavo.metri_reali || 0;\n    acc.da_posare += cavo.metri_da_posare || 0;\n    return acc;\n  }, { teorici: 0, reali: 0, da_posare: 0 });\n\n  const totaliData = [\n    { name: 'Metri Teorici', value: totaliCavi.teorici, color: COLORS.primary },\n    { name: 'Metri Reali', value: totaliCavi.reali, color: COLORS.success },\n    { name: 'Metri da Posare', value: totaliCavi.da_posare, color: COLORS.warning }\n  ];\n\n  // Analisi deficit/surplus\n  const analisiData = caviData.map(cavo => ({\n    tipologia: cavo.tipologia_short,\n    tipologia_full: cavo.tipologia,\n    deficit: cavo.deficit,\n    surplus: cavo.surplus,\n    necessita_acquisto: cavo.deficit > 0\n  }));\n\n  const CustomTooltip = ({ active, payload, label }) => {\n    if (active && payload && payload.length) {\n      return (\n        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>\n          <Typography variant=\"body2\">{`${label}`}</Typography>\n          {payload.map((entry, index) => (\n            <Typography key={index} variant=\"body2\" style={{ color: entry.color }}>\n              {`${entry.name}: ${typeof entry.value === 'number' ? entry.value.toFixed(2) : entry.value}`}\n            </Typography>\n          ))}\n        </Paper>\n      );\n    }\n    return null;\n  };\n\n  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {\n    if (percent < 0.05) return null;\n\n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n\n    return (\n      <text\n        x={x}\n        y={y}\n        fill=\"white\"\n        textAnchor={x > cx ? 'start' : 'end'}\n        dominantBaseline=\"central\"\n        fontSize=\"12\"\n        fontWeight=\"bold\"\n      >\n        {`${(percent * 100).toFixed(0)}%`}\n      </text>\n    );\n  };\n\n  return (\n    <Box sx={{ mt: 3 }}>\n      {/* Alert per Metri Orfani */}\n      {data.metri_orfani && data.metri_orfani.metri_orfani_totali > 0 && (\n        <Grid container spacing={2} sx={{ mb: 2 }}>\n          <Grid item xs={12}>\n            <Paper sx={{ p: 0, border: '2px solid #ff9800', borderRadius: 1, bgcolor: '#fff3e0' }}>\n              <Box sx={{ p: 2 }}>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#e65100', mb: 1 }}>\n                  ⚠️ METRI POSATI SENZA BOBINA ASSOCIATA\n                </Typography>\n                <Typography variant=\"body1\" sx={{ color: '#bf360c', fontWeight: 500, mb: 1 }}>\n                  <strong>{data.metri_orfani.metri_orfani_totali}m</strong> posati con BOBINA_VUOTA\n                  ({data.metri_orfani.num_cavi_orfani} cavi)\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: '#5d4037' }}>\n                  Questi metri sono stati installati ma non sono tracciati nel parco bobine.\n                  <br />\n                  <strong>Azione richiesta:</strong> Associare a bobine esistenti o registrare nuove bobine per tracciabilità completa.\n                </Typography>\n              </Box>\n            </Paper>\n          </Grid>\n        </Grid>\n      )}\n\n      {/* Tabella Bill of Quantities Unificata */}\n      <Grid container spacing={2}>\n        <Grid item xs={12}>\n          <Paper sx={{ p: 0, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n            <Box sx={{\n              bgcolor: '#f8f9fa',\n              p: 2,\n              borderBottom: '1px solid #e0e0e0'\n            }}>\n              <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                📋 Bill of Quantities - Distinta Materiali\n              </Typography>\n              <Typography variant=\"body2\" sx={{ color: '#666', mt: 0.5 }}>\n                Riepilogo completo dei materiali per tipologia di cavo\n              </Typography>\n            </Box>\n\n            <Box sx={{ overflow: 'auto' }}>\n              <table style={{ width: '100%', borderCollapse: 'collapse' }}>\n                <thead>\n                  <tr style={{ backgroundColor: '#f8f9fa' }}>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'left',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Tipologia</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Conduttori</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Sezione</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>N° Cavi</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Metri Teorici</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Metri Posati</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Metri Rimanenti</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Metri Disponibili</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Metri Mancanti</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Necessita Acquisto</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>% Completamento</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0'\n                    }}>Stato</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {caviData.map((cavo, index) => {\n                    const percentuale = cavo.metri_teorici > 0 ?\n                      ((cavo.metri_reali || 0) / cavo.metri_teorici * 100) : 0;\n                    const isCompleto = percentuale >= 100;\n                    const isInCorso = percentuale > 0 && percentuale < 100;\n\n                    return (\n                      <tr key={index} style={{\n                        backgroundColor: index % 2 === 0 ? '#ffffff' : '#fafafa'\n                      }}>\n                        <td style={{\n                          padding: '12px 16px',\n                          fontSize: '13px',\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0',\n                          fontWeight: 500\n                        }}>\n                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                            <Box sx={{\n                              width: '4px',\n                              height: '20px',\n                              backgroundColor: cavo.color,\n                              borderRadius: '2px'\n                            }} />\n                            {cavo.tipologia}\n                          </Box>\n                        </td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'center',\n                          fontSize: '13px',\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{cavo.conduttori || 'N/A'}</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'center',\n                          fontSize: '13px',\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{cavo.sezione || 'N/A'}</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'center',\n                          fontSize: '13px',\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{cavo.num_cavi}</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'right',\n                          fontSize: '13px',\n                          fontWeight: 600,\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{(cavo.metri_teorici || 0).toFixed(1)}m</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'right',\n                          fontSize: '13px',\n                          fontWeight: 600,\n                          color: COLORS.success,\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{(cavo.metri_reali || 0).toFixed(1)}m</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'right',\n                          fontSize: '13px',\n                          fontWeight: 600,\n                          color: COLORS.warning,\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{(cavo.metri_da_posare || 0).toFixed(1)}m</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'right',\n                          fontSize: '13px',\n                          fontWeight: 600,\n                          color: COLORS.info,\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{(cavo.metri_disponibili || 0).toFixed(1)}m</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'right',\n                          fontSize: '13px',\n                          fontWeight: 600,\n                          color: cavo.metri_mancanti > 0 ? COLORS.error : COLORS.success,\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{(cavo.metri_mancanti || 0).toFixed(1)}m</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'center',\n                          fontSize: '13px',\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>\n                          <Box sx={{\n                            display: 'inline-flex',\n                            alignItems: 'center',\n                            gap: 0.5,\n                            px: 1,\n                            py: 0.5,\n                            borderRadius: '12px',\n                            fontSize: '11px',\n                            fontWeight: 600,\n                            backgroundColor: cavo.necessita_acquisto ? '#ffebee' : '#e8f5e8',\n                            color: cavo.necessita_acquisto ? '#c62828' : '#2e7d32'\n                          }}>\n                            {cavo.necessita_acquisto ? '🛒 Sì' : '✅ No'}\n                          </Box>\n                        </td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'right',\n                          fontSize: '13px',\n                          fontWeight: 600,\n                          color: isCompleto ? COLORS.success : isInCorso ? COLORS.warning : COLORS.secondary,\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{percentuale.toFixed(1)}%</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'center',\n                          fontSize: '13px',\n                          borderBottom: '1px solid #f0f0f0'\n                        }}>\n                          <Box sx={{\n                            display: 'inline-flex',\n                            alignItems: 'center',\n                            gap: 0.5,\n                            px: 1,\n                            py: 0.5,\n                            borderRadius: '12px',\n                            fontSize: '11px',\n                            fontWeight: 600,\n                            backgroundColor: isCompleto ? '#e8f5e8' : isInCorso ? '#fff3cd' : '#f8f9fa',\n                            color: isCompleto ? '#2e7d32' : isInCorso ? '#856404' : '#6c757d'\n                          }}>\n                            {isCompleto ? '✅ Completato' : isInCorso ? '🔄 In Corso' : '⏳ Da Iniziare'}\n                          </Box>\n                        </td>\n                      </tr>\n                    );\n                  })}\n                </tbody>\n              </table>\n            </Box>\n\n            {/* Totali in fondo */}\n            <Box sx={{\n              bgcolor: '#f8f9fa',\n              p: 2,\n              borderTop: '1px solid #e0e0e0'\n            }}>\n              <Grid container spacing={3}>\n                <Grid item xs={12} sm={3}>\n                  <Box sx={{ textAlign: 'center' }}>\n                    <Typography variant=\"h6\" sx={{ color: COLORS.primary, fontWeight: 600 }}>\n                      {data.riepilogo?.totale_metri_teorici?.toFixed(1) || totaliCavi.teorici.toFixed(1)}m\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                      Metri Teorici Totali\n                    </Typography>\n                  </Box>\n                </Grid>\n                <Grid item xs={12} sm={3}>\n                  <Box sx={{ textAlign: 'center' }}>\n                    <Typography variant=\"h6\" sx={{ color: COLORS.success, fontWeight: 600 }}>\n                      {data.riepilogo?.totale_metri_posati?.toFixed(1) || totaliCavi.reali.toFixed(1)}m\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                      Metri Posati Totali\n                    </Typography>\n                  </Box>\n                </Grid>\n                <Grid item xs={12} sm={3}>\n                  <Box sx={{ textAlign: 'center' }}>\n                    <Typography variant=\"h6\" sx={{ color: COLORS.warning, fontWeight: 600 }}>\n                      {data.riepilogo?.totale_metri_da_posare?.toFixed(1) || totaliCavi.da_posare.toFixed(1)}m\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                      Metri Rimanenti\n                    </Typography>\n                  </Box>\n                </Grid>\n                <Grid item xs={12} sm={3}>\n                  <Box sx={{ textAlign: 'center' }}>\n                    <Typography variant=\"h6\" sx={{ color: COLORS.primary, fontWeight: 600 }}>\n                      {data.riepilogo?.percentuale_completamento?.toFixed(1) || (totaliCavi.teorici > 0 ? ((totaliCavi.reali / totaliCavi.teorici) * 100).toFixed(1) : 0)}%\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                      Completamento Totale\n                    </Typography>\n                  </Box>\n                </Grid>\n              </Grid>\n            </Box>\n          </Paper>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default BoqChart;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,QAAQ,EACRC,GAAG,EACHC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,EACnBC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,aAAa,EACbC,IAAI,EACJC,SAAS,QACJ,UAAU;AACjB,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,MAAM,GAAG;EACbC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,SAAS;EAChBC,MAAM,EAAE,SAAS;EACjBC,IAAI,EAAE;AACR,CAAC;AAED,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;EAC7B,IAAI,CAACV,IAAI,EAAE,OAAO,IAAI;;EAEtB;EACA,MAAMW,QAAQ,GAAG,EAAAV,qBAAA,GAAAD,IAAI,CAACY,kBAAkB,cAAAX,qBAAA,uBAAvBA,qBAAA,CAAyBY,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK;IAAA,IAAAC,eAAA;IAAA,OAAM;MAC9D,GAAGF,IAAI;MACP;MACAG,aAAa,EAAEH,IAAI,CAACI,oBAAoB;MACxCC,WAAW,EAAEL,IAAI,CAACM,kBAAkB;MACpCC,eAAe,EAAE,EAAAL,eAAA,GAAAF,IAAI,CAACQ,SAAS,cAAAN,eAAA,uBAAdA,eAAA,CAAgBO,MAAM,IAAG,CAAC,GAAGT,IAAI,CAACQ,SAAS,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAGV,IAAI,CAACQ,SAAS;MACrGG,KAAK,EAAEC,MAAM,CAACC,MAAM,CAACrC,MAAM,CAAC,CAACyB,KAAK,GAAGW,MAAM,CAACC,MAAM,CAACrC,MAAM,CAAC,CAACiC,MAAM,CAAC;MAClEK,OAAO,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEhB,IAAI,CAACiB,eAAe,IAAIjB,IAAI,CAACM,kBAAkB,IAAI,CAAC,CAAC,CAAC;MAC3EY,OAAO,EAAEH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAChB,IAAI,CAACM,kBAAkB,IAAI,CAAC,IAAIN,IAAI,CAACiB,eAAe;IAC5E,CAAC;EAAA,CAAC,CAAC,KAAI,EAAE;;EAET;EACA,MAAME,UAAU,GAAG,EAAA/B,sBAAA,GAAAF,IAAI,CAACY,kBAAkB,cAAAV,sBAAA,uBAAvBA,sBAAA,CAAyBgC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC,CAACvB,GAAG,CAAC,CAACwB,MAAM,EAAEtB,KAAK;IAAA,IAAAuB,iBAAA;IAAA,OAAM;MACtGhB,SAAS,EAAEe,MAAM,CAACf,SAAS;MAC3BiB,OAAO,EAAEF,MAAM,CAACE,OAAO;MACvBH,UAAU,EAAEC,MAAM,CAACD,UAAU;MAC7BI,iBAAiB,EAAEH,MAAM,CAACG,iBAAiB;MAC3CnB,eAAe,EAAE,EAAAiB,iBAAA,GAAAD,MAAM,CAACf,SAAS,cAAAgB,iBAAA,uBAAhBA,iBAAA,CAAkBf,MAAM,IAAG,CAAC,GAAGc,MAAM,CAACf,SAAS,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAGa,MAAM,CAACf,SAAS;MAC3GG,KAAK,EAAEC,MAAM,CAACC,MAAM,CAACrC,MAAM,CAAC,CAACyB,KAAK,GAAGW,MAAM,CAACC,MAAM,CAACrC,MAAM,CAAC,CAACiC,MAAM;IACnE,CAAC;EAAA,CAAC,CAAC,KAAI,EAAE;;EAET;EACA,MAAMkB,UAAU,GAAG9B,QAAQ,CAAC+B,MAAM,CAAC,CAACC,GAAG,EAAE7B,IAAI,KAAK;IAChD6B,GAAG,CAACC,OAAO,IAAI9B,IAAI,CAACG,aAAa,IAAI,CAAC;IACtC0B,GAAG,CAACE,KAAK,IAAI/B,IAAI,CAACK,WAAW,IAAI,CAAC;IAClCwB,GAAG,CAACG,SAAS,IAAIhC,IAAI,CAACiB,eAAe,IAAI,CAAC;IAC1C,OAAOY,GAAG;EACZ,CAAC,EAAE;IAAEC,OAAO,EAAE,CAAC;IAAEC,KAAK,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAE,CAAC,CAAC;EAE1C,MAAMC,UAAU,GAAG,CACjB;IAAEC,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAER,UAAU,CAACG,OAAO;IAAEnB,KAAK,EAAEnC,MAAM,CAACC;EAAQ,CAAC,EAC3E;IAAEyD,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAER,UAAU,CAACI,KAAK;IAAEpB,KAAK,EAAEnC,MAAM,CAACG;EAAQ,CAAC,EACvE;IAAEuD,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAER,UAAU,CAACK,SAAS;IAAErB,KAAK,EAAEnC,MAAM,CAACI;EAAQ,CAAC,CAChF;;EAED;EACA,MAAMwD,WAAW,GAAGvC,QAAQ,CAACE,GAAG,CAACC,IAAI,KAAK;IACxCQ,SAAS,EAAER,IAAI,CAACO,eAAe;IAC/B8B,cAAc,EAAErC,IAAI,CAACQ,SAAS;IAC9BM,OAAO,EAAEd,IAAI,CAACc,OAAO;IACrBI,OAAO,EAAElB,IAAI,CAACkB,OAAO;IACrBoB,kBAAkB,EAAEtC,IAAI,CAACc,OAAO,GAAG;EACrC,CAAC,CAAC,CAAC;EAEH,MAAMyB,aAAa,GAAGA,CAAC;IAAEC,MAAM;IAAEC,OAAO;IAAEC;EAAM,CAAC,KAAK;IACpD,IAAIF,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAAChC,MAAM,EAAE;MACvC,oBACElC,OAAA,CAACH,KAAK;QAACuE,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAiB,CAAE;QAAAC,QAAA,gBAC5CvE,OAAA,CAACL,UAAU;UAAC6E,OAAO,EAAC,OAAO;UAAAD,QAAA,EAAE,GAAGJ,KAAK;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,EACpDV,OAAO,CAAC1C,GAAG,CAAC,CAACqD,KAAK,EAAEnD,KAAK,kBACxB1B,OAAA,CAACL,UAAU;UAAa6E,OAAO,EAAC,OAAO;UAACM,KAAK,EAAE;YAAE1C,KAAK,EAAEyC,KAAK,CAACzC;UAAM,CAAE;UAAAmC,QAAA,EACnE,GAAGM,KAAK,CAAClB,IAAI,KAAK,OAAOkB,KAAK,CAACjB,KAAK,KAAK,QAAQ,GAAGiB,KAAK,CAACjB,KAAK,CAACmB,OAAO,CAAC,CAAC,CAAC,GAAGF,KAAK,CAACjB,KAAK;QAAE,GAD5ElC,KAAK;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAEZ;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMI,qBAAqB,GAAGA,CAAC;IAAEC,EAAE;IAAEC,EAAE;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,WAAW;IAAEC;EAAQ,CAAC,KAAK;IACzF,IAAIA,OAAO,GAAG,IAAI,EAAE,OAAO,IAAI;IAE/B,MAAMC,MAAM,GAAG/C,IAAI,CAACgD,EAAE,GAAG,GAAG;IAC5B,MAAMC,MAAM,GAAGL,WAAW,GAAG,CAACC,WAAW,GAAGD,WAAW,IAAI,GAAG;IAC9D,MAAMM,CAAC,GAAGT,EAAE,GAAGQ,MAAM,GAAGjD,IAAI,CAACmD,GAAG,CAAC,CAACR,QAAQ,GAAGI,MAAM,CAAC;IACpD,MAAMK,CAAC,GAAGV,EAAE,GAAGO,MAAM,GAAGjD,IAAI,CAACqD,GAAG,CAAC,CAACV,QAAQ,GAAGI,MAAM,CAAC;IAEpD,oBACEvF,OAAA;MACE0F,CAAC,EAAEA,CAAE;MACLE,CAAC,EAAEA,CAAE;MACLE,IAAI,EAAC,OAAO;MACZC,UAAU,EAAEL,CAAC,GAAGT,EAAE,GAAG,OAAO,GAAG,KAAM;MACrCe,gBAAgB,EAAC,SAAS;MAC1BC,QAAQ,EAAC,IAAI;MACbC,UAAU,EAAC,MAAM;MAAA3B,QAAA,EAEhB,GAAG,CAACe,OAAO,GAAG,GAAG,EAAEP,OAAO,CAAC,CAAC,CAAC;IAAG;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAEX,CAAC;EAED,oBACE5E,OAAA,CAACN,GAAG;IAAC0E,EAAE,EAAE;MAAE+B,EAAE,EAAE;IAAE,CAAE;IAAA5B,QAAA,GAEhB5D,IAAI,CAACyF,YAAY,IAAIzF,IAAI,CAACyF,YAAY,CAACC,mBAAmB,GAAG,CAAC,iBAC7DrG,OAAA,CAACJ,IAAI;MAAC0G,SAAS;MAACC,OAAO,EAAE,CAAE;MAACnC,EAAE,EAAE;QAAEoC,EAAE,EAAE;MAAE,CAAE;MAAAjC,QAAA,eACxCvE,OAAA,CAACJ,IAAI;QAACkD,IAAI;QAAC2D,EAAE,EAAE,EAAG;QAAAlC,QAAA,eAChBvE,OAAA,CAACH,KAAK;UAACuE,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEC,MAAM,EAAE,mBAAmB;YAAEoC,YAAY,EAAE,CAAC;YAAEC,OAAO,EAAE;UAAU,CAAE;UAAApC,QAAA,eACpFvE,OAAA,CAACN,GAAG;YAAC0E,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAE,CAAE;YAAAE,QAAA,gBAChBvE,OAAA,CAACL,UAAU;cAAC6E,OAAO,EAAC,IAAI;cAACJ,EAAE,EAAE;gBAAE8B,UAAU,EAAE,GAAG;gBAAE9D,KAAK,EAAE,SAAS;gBAAEoE,EAAE,EAAE;cAAE,CAAE;cAAAjC,QAAA,EAAC;YAE3E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb5E,OAAA,CAACL,UAAU;cAAC6E,OAAO,EAAC,OAAO;cAACJ,EAAE,EAAE;gBAAEhC,KAAK,EAAE,SAAS;gBAAE8D,UAAU,EAAE,GAAG;gBAAEM,EAAE,EAAE;cAAE,CAAE;cAAAjC,QAAA,gBAC3EvE,OAAA;gBAAAuE,QAAA,GAAS5D,IAAI,CAACyF,YAAY,CAACC,mBAAmB,EAAC,GAAC;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,8BACxD,EAACjE,IAAI,CAACyF,YAAY,CAACQ,eAAe,EAAC,QACtC;YAAA;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb5E,OAAA,CAACL,UAAU;cAAC6E,OAAO,EAAC,OAAO;cAACJ,EAAE,EAAE;gBAAEhC,KAAK,EAAE;cAAU,CAAE;cAAAmC,QAAA,GAAC,4EAEpD,eAAAvE,OAAA;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN5E,OAAA;gBAAAuE,QAAA,EAAQ;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,0FACpC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACP,eAGD5E,OAAA,CAACJ,IAAI;MAAC0G,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAhC,QAAA,eACzBvE,OAAA,CAACJ,IAAI;QAACkD,IAAI;QAAC2D,EAAE,EAAE,EAAG;QAAAlC,QAAA,eAChBvE,OAAA,CAACH,KAAK;UAACuE,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEC,MAAM,EAAE,mBAAmB;YAAEoC,YAAY,EAAE;UAAE,CAAE;UAAAnC,QAAA,gBAChEvE,OAAA,CAACN,GAAG;YAAC0E,EAAE,EAAE;cACPuC,OAAO,EAAE,SAAS;cAClBtC,CAAC,EAAE,CAAC;cACJwC,YAAY,EAAE;YAChB,CAAE;YAAAtC,QAAA,gBACAvE,OAAA,CAACL,UAAU;cAAC6E,OAAO,EAAC,IAAI;cAACJ,EAAE,EAAE;gBAAE8B,UAAU,EAAE,GAAG;gBAAE9D,KAAK,EAAE;cAAU,CAAE;cAAAmC,QAAA,EAAC;YAEpE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb5E,OAAA,CAACL,UAAU;cAAC6E,OAAO,EAAC,OAAO;cAACJ,EAAE,EAAE;gBAAEhC,KAAK,EAAE,MAAM;gBAAE+D,EAAE,EAAE;cAAI,CAAE;cAAA5B,QAAA,EAAC;YAE5D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEN5E,OAAA,CAACN,GAAG;YAAC0E,EAAE,EAAE;cAAE0C,QAAQ,EAAE;YAAO,CAAE;YAAAvC,QAAA,eAC5BvE,OAAA;cAAO8E,KAAK,EAAE;gBAAEiC,KAAK,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAW,CAAE;cAAAzC,QAAA,gBAC1DvE,OAAA;gBAAAuE,QAAA,eACEvE,OAAA;kBAAI8E,KAAK,EAAE;oBAAEmC,eAAe,EAAE;kBAAU,CAAE;kBAAA1C,QAAA,gBACxCvE,OAAA;oBAAI8E,KAAK,EAAE;sBACToC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,MAAM;sBACjBlB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChByE,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAA7C,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjB5E,OAAA;oBAAI8E,KAAK,EAAE;sBACToC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,QAAQ;sBACnBlB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChByE,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAA7C,QAAA,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClB5E,OAAA;oBAAI8E,KAAK,EAAE;sBACToC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,QAAQ;sBACnBlB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChByE,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAA7C,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACf5E,OAAA;oBAAI8E,KAAK,EAAE;sBACToC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,QAAQ;sBACnBlB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChByE,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAA7C,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACf5E,OAAA;oBAAI8E,KAAK,EAAE;sBACToC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,OAAO;sBAClBlB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChByE,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAA7C,QAAA,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrB5E,OAAA;oBAAI8E,KAAK,EAAE;sBACToC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,OAAO;sBAClBlB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChByE,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAA7C,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpB5E,OAAA;oBAAI8E,KAAK,EAAE;sBACToC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,OAAO;sBAClBlB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChByE,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAA7C,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvB5E,OAAA;oBAAI8E,KAAK,EAAE;sBACToC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,OAAO;sBAClBlB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChByE,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAA7C,QAAA,EAAC;kBAAiB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzB5E,OAAA;oBAAI8E,KAAK,EAAE;sBACToC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,OAAO;sBAClBlB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChByE,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAA7C,QAAA,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtB5E,OAAA;oBAAI8E,KAAK,EAAE;sBACToC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,QAAQ;sBACnBlB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChByE,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAA7C,QAAA,EAAC;kBAAkB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1B5E,OAAA;oBAAI8E,KAAK,EAAE;sBACToC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,OAAO;sBAClBlB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChByE,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAA7C,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvB5E,OAAA;oBAAI8E,KAAK,EAAE;sBACToC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,QAAQ;sBACnBlB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChByE,YAAY,EAAE;oBAChB,CAAE;oBAAAtC,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR5E,OAAA;gBAAAuE,QAAA,EACGjD,QAAQ,CAACE,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;kBAC7B,MAAM2F,WAAW,GAAG5F,IAAI,CAACG,aAAa,GAAG,CAAC,GACvC,CAACH,IAAI,CAACK,WAAW,IAAI,CAAC,IAAIL,IAAI,CAACG,aAAa,GAAG,GAAG,GAAI,CAAC;kBAC1D,MAAM0F,UAAU,GAAGD,WAAW,IAAI,GAAG;kBACrC,MAAME,SAAS,GAAGF,WAAW,GAAG,CAAC,IAAIA,WAAW,GAAG,GAAG;kBAEtD,oBACErH,OAAA;oBAAgB8E,KAAK,EAAE;sBACrBmC,eAAe,EAAEvF,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG;oBACjD,CAAE;oBAAA6C,QAAA,gBACAvE,OAAA;sBAAI8E,KAAK,EAAE;wBACToC,OAAO,EAAE,WAAW;wBACpBjB,QAAQ,EAAE,MAAM;wBAChBY,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE,mBAAmB;wBAChClB,UAAU,EAAE;sBACd,CAAE;sBAAA3B,QAAA,eACAvE,OAAA,CAACN,GAAG;wBAAC0E,EAAE,EAAE;0BAAEoD,OAAO,EAAE,MAAM;0BAAEC,UAAU,EAAE,QAAQ;0BAAEC,GAAG,EAAE;wBAAE,CAAE;wBAAAnD,QAAA,gBACzDvE,OAAA,CAACN,GAAG;0BAAC0E,EAAE,EAAE;4BACP2C,KAAK,EAAE,KAAK;4BACZY,MAAM,EAAE,MAAM;4BACdV,eAAe,EAAExF,IAAI,CAACW,KAAK;4BAC3BsE,YAAY,EAAE;0BAChB;wBAAE;0BAAAjC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACJnD,IAAI,CAACQ,SAAS;sBAAA;wBAAAwC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACL5E,OAAA;sBAAI8E,KAAK,EAAE;wBACToC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,QAAQ;wBACnBlB,QAAQ,EAAE,MAAM;wBAChBY,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAA7C,QAAA,EAAE9C,IAAI,CAACmG,UAAU,IAAI;oBAAK;sBAAAnD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAClC5E,OAAA;sBAAI8E,KAAK,EAAE;wBACToC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,QAAQ;wBACnBlB,QAAQ,EAAE,MAAM;wBAChBY,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAA7C,QAAA,EAAE9C,IAAI,CAACyB,OAAO,IAAI;oBAAK;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC/B5E,OAAA;sBAAI8E,KAAK,EAAE;wBACToC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,QAAQ;wBACnBlB,QAAQ,EAAE,MAAM;wBAChBY,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAA7C,QAAA,EAAE9C,IAAI,CAACoG;oBAAQ;sBAAApD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvB5E,OAAA;sBAAI8E,KAAK,EAAE;wBACToC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,OAAO;wBAClBlB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,GAAG;wBACfW,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAA7C,QAAA,GAAE,CAAC9C,IAAI,CAACG,aAAa,IAAI,CAAC,EAAEmD,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/C5E,OAAA;sBAAI8E,KAAK,EAAE;wBACToC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,OAAO;wBAClBlB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,GAAG;wBACf9D,KAAK,EAAEnC,MAAM,CAACG,OAAO;wBACrByG,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAA7C,QAAA,GAAE,CAAC9C,IAAI,CAACK,WAAW,IAAI,CAAC,EAAEiD,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC7C5E,OAAA;sBAAI8E,KAAK,EAAE;wBACToC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,OAAO;wBAClBlB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,GAAG;wBACf9D,KAAK,EAAEnC,MAAM,CAACI,OAAO;wBACrBwG,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAA7C,QAAA,GAAE,CAAC9C,IAAI,CAACiB,eAAe,IAAI,CAAC,EAAEqC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjD5E,OAAA;sBAAI8E,KAAK,EAAE;wBACToC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,OAAO;wBAClBlB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,GAAG;wBACf9D,KAAK,EAAEnC,MAAM,CAACK,IAAI;wBAClBuG,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAA7C,QAAA,GAAE,CAAC9C,IAAI,CAAC0B,iBAAiB,IAAI,CAAC,EAAE4B,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnD5E,OAAA;sBAAI8E,KAAK,EAAE;wBACToC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,OAAO;wBAClBlB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,GAAG;wBACf9D,KAAK,EAAEX,IAAI,CAACqG,cAAc,GAAG,CAAC,GAAG7H,MAAM,CAACM,KAAK,GAAGN,MAAM,CAACG,OAAO;wBAC9DyG,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAA7C,QAAA,GAAE,CAAC9C,IAAI,CAACqG,cAAc,IAAI,CAAC,EAAE/C,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAChD5E,OAAA;sBAAI8E,KAAK,EAAE;wBACToC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,QAAQ;wBACnBlB,QAAQ,EAAE,MAAM;wBAChBY,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAA7C,QAAA,eACAvE,OAAA,CAACN,GAAG;wBAAC0E,EAAE,EAAE;0BACPoD,OAAO,EAAE,aAAa;0BACtBC,UAAU,EAAE,QAAQ;0BACpBC,GAAG,EAAE,GAAG;0BACRK,EAAE,EAAE,CAAC;0BACLC,EAAE,EAAE,GAAG;0BACPtB,YAAY,EAAE,MAAM;0BACpBT,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE,GAAG;0BACfe,eAAe,EAAExF,IAAI,CAACsC,kBAAkB,GAAG,SAAS,GAAG,SAAS;0BAChE3B,KAAK,EAAEX,IAAI,CAACsC,kBAAkB,GAAG,SAAS,GAAG;wBAC/C,CAAE;wBAAAQ,QAAA,EACC9C,IAAI,CAACsC,kBAAkB,GAAG,OAAO,GAAG;sBAAM;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACL5E,OAAA;sBAAI8E,KAAK,EAAE;wBACToC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,OAAO;wBAClBlB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,GAAG;wBACf9D,KAAK,EAAEkF,UAAU,GAAGrH,MAAM,CAACG,OAAO,GAAGmH,SAAS,GAAGtH,MAAM,CAACI,OAAO,GAAGJ,MAAM,CAACE,SAAS;wBAClF0G,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAA7C,QAAA,GAAE8C,WAAW,CAACtC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjC5E,OAAA;sBAAI8E,KAAK,EAAE;wBACToC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,QAAQ;wBACnBlB,QAAQ,EAAE,MAAM;wBAChBY,YAAY,EAAE;sBAChB,CAAE;sBAAAtC,QAAA,eACAvE,OAAA,CAACN,GAAG;wBAAC0E,EAAE,EAAE;0BACPoD,OAAO,EAAE,aAAa;0BACtBC,UAAU,EAAE,QAAQ;0BACpBC,GAAG,EAAE,GAAG;0BACRK,EAAE,EAAE,CAAC;0BACLC,EAAE,EAAE,GAAG;0BACPtB,YAAY,EAAE,MAAM;0BACpBT,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE,GAAG;0BACfe,eAAe,EAAEK,UAAU,GAAG,SAAS,GAAGC,SAAS,GAAG,SAAS,GAAG,SAAS;0BAC3EnF,KAAK,EAAEkF,UAAU,GAAG,SAAS,GAAGC,SAAS,GAAG,SAAS,GAAG;wBAC1D,CAAE;wBAAAhD,QAAA,EACC+C,UAAU,GAAG,cAAc,GAAGC,SAAS,GAAG,aAAa,GAAG;sBAAe;wBAAA9C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA,GAxIElD,KAAK;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAyIV,CAAC;gBAET,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGN5E,OAAA,CAACN,GAAG;YAAC0E,EAAE,EAAE;cACPuC,OAAO,EAAE,SAAS;cAClBtC,CAAC,EAAE,CAAC;cACJ4D,SAAS,EAAE;YACb,CAAE;YAAA1D,QAAA,eACAvE,OAAA,CAACJ,IAAI;cAAC0G,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAhC,QAAA,gBACzBvE,OAAA,CAACJ,IAAI;gBAACkD,IAAI;gBAAC2D,EAAE,EAAE,EAAG;gBAACyB,EAAE,EAAE,CAAE;gBAAA3D,QAAA,eACvBvE,OAAA,CAACN,GAAG;kBAAC0E,EAAE,EAAE;oBAAE+C,SAAS,EAAE;kBAAS,CAAE;kBAAA5C,QAAA,gBAC/BvE,OAAA,CAACL,UAAU;oBAAC6E,OAAO,EAAC,IAAI;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAEnC,MAAM,CAACC,OAAO;sBAAEgG,UAAU,EAAE;oBAAI,CAAE;oBAAA3B,QAAA,GACrE,EAAAzD,eAAA,GAAAH,IAAI,CAACwH,SAAS,cAAArH,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBsH,oBAAoB,cAAArH,qBAAA,uBAApCA,qBAAA,CAAsCgE,OAAO,CAAC,CAAC,CAAC,KAAI3B,UAAU,CAACG,OAAO,CAACwB,OAAO,CAAC,CAAC,CAAC,EAAC,GACrF;kBAAA;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb5E,OAAA,CAACL,UAAU;oBAAC6E,OAAO,EAAC,SAAS;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAE;oBAAO,CAAE;oBAAAmC,QAAA,EAAC;kBAErD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACP5E,OAAA,CAACJ,IAAI;gBAACkD,IAAI;gBAAC2D,EAAE,EAAE,EAAG;gBAACyB,EAAE,EAAE,CAAE;gBAAA3D,QAAA,eACvBvE,OAAA,CAACN,GAAG;kBAAC0E,EAAE,EAAE;oBAAE+C,SAAS,EAAE;kBAAS,CAAE;kBAAA5C,QAAA,gBAC/BvE,OAAA,CAACL,UAAU;oBAAC6E,OAAO,EAAC,IAAI;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAEnC,MAAM,CAACG,OAAO;sBAAE8F,UAAU,EAAE;oBAAI,CAAE;oBAAA3B,QAAA,GACrE,EAAAvD,gBAAA,GAAAL,IAAI,CAACwH,SAAS,cAAAnH,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBqH,mBAAmB,cAAApH,qBAAA,uBAAnCA,qBAAA,CAAqC8D,OAAO,CAAC,CAAC,CAAC,KAAI3B,UAAU,CAACI,KAAK,CAACuB,OAAO,CAAC,CAAC,CAAC,EAAC,GAClF;kBAAA;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb5E,OAAA,CAACL,UAAU;oBAAC6E,OAAO,EAAC,SAAS;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAE;oBAAO,CAAE;oBAAAmC,QAAA,EAAC;kBAErD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACP5E,OAAA,CAACJ,IAAI;gBAACkD,IAAI;gBAAC2D,EAAE,EAAE,EAAG;gBAACyB,EAAE,EAAE,CAAE;gBAAA3D,QAAA,eACvBvE,OAAA,CAACN,GAAG;kBAAC0E,EAAE,EAAE;oBAAE+C,SAAS,EAAE;kBAAS,CAAE;kBAAA5C,QAAA,gBAC/BvE,OAAA,CAACL,UAAU;oBAAC6E,OAAO,EAAC,IAAI;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAEnC,MAAM,CAACI,OAAO;sBAAE6F,UAAU,EAAE;oBAAI,CAAE;oBAAA3B,QAAA,GACrE,EAAArD,gBAAA,GAAAP,IAAI,CAACwH,SAAS,cAAAjH,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBoH,sBAAsB,cAAAnH,qBAAA,uBAAtCA,qBAAA,CAAwC4D,OAAO,CAAC,CAAC,CAAC,KAAI3B,UAAU,CAACK,SAAS,CAACsB,OAAO,CAAC,CAAC,CAAC,EAAC,GACzF;kBAAA;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb5E,OAAA,CAACL,UAAU;oBAAC6E,OAAO,EAAC,SAAS;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAE;oBAAO,CAAE;oBAAAmC,QAAA,EAAC;kBAErD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACP5E,OAAA,CAACJ,IAAI;gBAACkD,IAAI;gBAAC2D,EAAE,EAAE,EAAG;gBAACyB,EAAE,EAAE,CAAE;gBAAA3D,QAAA,eACvBvE,OAAA,CAACN,GAAG;kBAAC0E,EAAE,EAAE;oBAAE+C,SAAS,EAAE;kBAAS,CAAE;kBAAA5C,QAAA,gBAC/BvE,OAAA,CAACL,UAAU;oBAAC6E,OAAO,EAAC,IAAI;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAEnC,MAAM,CAACC,OAAO;sBAAEgG,UAAU,EAAE;oBAAI,CAAE;oBAAA3B,QAAA,GACrE,EAAAnD,gBAAA,GAAAT,IAAI,CAACwH,SAAS,cAAA/G,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBmH,yBAAyB,cAAAlH,qBAAA,uBAAzCA,qBAAA,CAA2C0D,OAAO,CAAC,CAAC,CAAC,MAAK3B,UAAU,CAACG,OAAO,GAAG,CAAC,GAAG,CAAEH,UAAU,CAACI,KAAK,GAAGJ,UAAU,CAACG,OAAO,GAAI,GAAG,EAAEwB,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAC,GACtJ;kBAAA;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb5E,OAAA,CAACL,UAAU;oBAAC6E,OAAO,EAAC,SAAS;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAE;oBAAO,CAAE;oBAAAmC,QAAA,EAAC;kBAErD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC4D,EAAA,GAjcI9H,QAAQ;AAmcd,eAAeA,QAAQ;AAAC,IAAA8H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}