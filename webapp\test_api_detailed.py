#!/usr/bin/env python3
"""
Script per testare l'API BOQ con dettagli di debug
"""

import requests
import json

def test_api_detailed():
    """Testa l'API BOQ con dettagli di debug."""
    
    print("=== TEST API BOQ DETTAGLIATO ===\n")
    
    base_url = "http://localhost:8002"
    cantiere_id = 1
    
    # Test endpoint BOQ
    boq_url = f"{base_url}/api/reports/{cantiere_id}/boq?formato=video"
    
    try:
        print(f"🔗 Chiamando: {boq_url}")
        response = requests.get(boq_url, timeout=30)
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📊 Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ JSON parsato correttamente")
                
                # Stampa la struttura completa della risposta
                print(f"\n📋 STRUTTURA RISPOSTA:")
                print(f"Keys: {list(data.keys())}")
                
                success = data.get('success', False)
                content = data.get('content', {})
                formato = data.get('formato', 'N/A')
                
                print(f"Success: {success}")
                print(f"Formato: {formato}")
                print(f"Content keys: {list(content.keys()) if isinstance(content, dict) else 'Non è un dict'}")
                
                if isinstance(content, dict):
                    nome_cantiere = content.get('nome_cantiere', 'N/A')
                    distinta = content.get('distinta_materiali', [])
                    riepilogo = content.get('riepilogo', {})
                    
                    print(f"\n📊 CONTENUTO:")
                    print(f"Nome cantiere: {nome_cantiere}")
                    print(f"Numero categorie: {len(distinta) if isinstance(distinta, list) else 'Non è una lista'}")
                    print(f"Riepilogo keys: {list(riepilogo.keys()) if isinstance(riepilogo, dict) else 'Non è un dict'}")
                    
                    if isinstance(distinta, list) and len(distinta) > 0:
                        print(f"\n🔍 PRIMA CATEGORIA:")
                        first_item = distinta[0]
                        print(f"Keys: {list(first_item.keys()) if isinstance(first_item, dict) else 'Non è un dict'}")
                        if isinstance(first_item, dict):
                            print(f"Tipologia: {first_item.get('tipologia', 'N/A')}")
                            print(f"Formazione: {first_item.get('formazione', 'N/A')}")
                            print(f"Metri acquistati: {first_item.get('metri_acquistati', 'N/A')}")
                            print(f"Metri residui: {first_item.get('metri_residui', 'N/A')}")
                    else:
                        print(f"\n❌ NESSUNA CATEGORIA NELLA DISTINTA!")
                        print(f"Tipo distinta: {type(distinta)}")
                        print(f"Valore distinta: {distinta}")
                else:
                    print(f"\n❌ CONTENT NON È UN DICT!")
                    print(f"Tipo content: {type(content)}")
                    print(f"Valore content: {content}")
                
            except json.JSONDecodeError as je:
                print(f"❌ Errore parsing JSON: {je}")
                print(f"Risposta raw: {response.text[:500]}...")
                
        elif response.status_code == 500:
            print(f"❌ Errore server 500")
            try:
                error_data = response.json()
                print(f"Dettagli errore: {error_data}")
            except:
                print(f"Risposta raw: {response.text}")
        else:
            print(f"❌ Status code inaspettato: {response.status_code}")
            print(f"Risposta: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Errore: Impossibile connettersi al backend!")
    except requests.exceptions.Timeout:
        print("❌ Errore: Timeout della richiesta!")
    except Exception as e:
        print(f"❌ Errore imprevisto: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_api_detailed()
