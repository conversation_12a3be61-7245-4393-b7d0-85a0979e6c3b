#!/usr/bin/env python3
"""
Test finale per verificare che il BOQ funzioni correttamente
"""

import requests
import json

def test_final_boq():
    """Test finale del BOQ con la nuova logica."""
    
    print("=== TEST FINALE BOQ ===\n")
    
    base_url = "http://localhost:8002"
    cantiere_id = 1
    
    # Test endpoint BOQ
    boq_url = f"{base_url}/api/reports/{cantiere_id}/boq?formato=video"
    
    try:
        print(f"🔗 Chiamando: {boq_url}")
        response = requests.get(boq_url, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            content = data.get('content', {})
            
            distinta = content.get('distinta_materiali', [])
            riepilogo = content.get('riepilogo', {})
            
            print(f"✅ API funziona correttamente")
            print(f"Nome cantiere: {content.get('nome_cantiere', 'N/A')}")
            print(f"Numero categorie: {len(distinta)}")
            
            # Cerca il caso specifico FG16OR16 1X240MM2
            caso_trovato = False
            
            for item in distinta:
                tipologia = item.get('tipologia', 'N/A')
                formazione = item.get('formazione', 'N/A')
                
                if tipologia == "FG16OR16" and "240MM2" in formazione:
                    caso_trovato = True
                    
                    metri_acquistati = item.get('metri_acquistati', 0)
                    metri_residui = item.get('metri_residui', 0)
                    metri_da_posare = item.get('metri_da_posare', 0)
                    metri_mancanti = item.get('metri_mancanti', 0)
                    necessita_acquisto = item.get('necessita_acquisto', True)
                    
                    print(f"\n🎯 CASO SPECIFICO: {tipologia} {formazione}")
                    print(f"   Metri acquistati: {metri_acquistati}m")
                    print(f"   Metri residui: {metri_residui}m")
                    print(f"   Metri da posare: {metri_da_posare}m")
                    print(f"   Metri mancanti: {metri_mancanti}m")
                    print(f"   Necessita acquisto: {'Sì' if necessita_acquisto else 'No'}")
                    
                    # Verifica logica
                    metri_mancanti_calcolati = max(0, metri_da_posare - metri_residui)
                    necessita_acquisto_calcolato = metri_mancanti_calcolati > 0
                    
                    print(f"\n🔍 VERIFICA LOGICA:")
                    print(f"   Metri mancanti calcolati: {metri_mancanti_calcolati}m")
                    print(f"   Necessita acquisto calcolato: {'Sì' if necessita_acquisto_calcolato else 'No'}")
                    
                    if (metri_mancanti == metri_mancanti_calcolati and 
                        necessita_acquisto == necessita_acquisto_calcolato and
                        metri_residui > 0 and not necessita_acquisto):
                        print(f"   🎉 LOGICA CORRETTA!")
                        print(f"   ✅ Bobina riconosciuta: {metri_residui}m disponibili")
                        print(f"   ✅ Non serve acquistare: {metri_residui}m > {metri_da_posare}m")
                    else:
                        print(f"   ❌ LOGICA ERRATA!")
                    
                    break
            
            if not caso_trovato:
                print("❌ CASO SPECIFICO FG16OR16 1X240MM2 NON TROVATO!")
            
            # Verifica riepilogo
            print(f"\n📊 RIEPILOGO:")
            print(f"   Totale metri acquistati: {riepilogo.get('totale_metri_acquistati', 0)}m")
            print(f"   Totale metri residui: {riepilogo.get('totale_metri_residui', 0)}m")
            print(f"   Totale metri mancanti: {riepilogo.get('totale_metri_mancanti', 0)}m")
            print(f"   Categorie che necessitano acquisto: {riepilogo.get('categorie_necessitano_acquisto', 0)}")
            
            # Verifica che i nuovi campi esistano
            if ('totale_metri_acquistati' in riepilogo and 
                'totale_metri_residui' in riepilogo):
                print(f"   ✅ Nuovi campi presenti nel riepilogo")
            else:
                print(f"   ❌ Nuovi campi mancanti nel riepilogo")
            
            # Verifica che i vecchi campi non esistano più
            if 'totale_metri_disponibili' not in riepilogo:
                print(f"   ✅ Campo obsoleto 'totale_metri_disponibili' rimosso")
            else:
                print(f"   ⚠️ Campo obsoleto 'totale_metri_disponibili' ancora presente")
            
            print(f"\n🎯 RISULTATO FINALE:")
            if caso_trovato and metri_residui > 0 and not necessita_acquisto:
                print(f"   🎉 SUCCESSO COMPLETO!")
                print(f"   ✅ Problema risolto: la bobina viene riconosciuta correttamente")
                print(f"   ✅ Logica corretta: Metri Mancanti = max(0, Metri da Posare - Metri Residui)")
                print(f"   ✅ Matching case-insensitive funziona: 'FG16OR16 1x240MM2' ↔ 'FG16OR16 1X240MM2'")
                print(f"   ✅ Nuova struttura dati implementata correttamente")
            else:
                print(f"   ❌ PROBLEMA NON RISOLTO")
                
        else:
            print(f"❌ Errore API: {response.status_code}")
            print(f"Risposta: {response.text}")
            
    except Exception as e:
        print(f"❌ Errore durante il test: {e}")

if __name__ == "__main__":
    test_final_boq()
