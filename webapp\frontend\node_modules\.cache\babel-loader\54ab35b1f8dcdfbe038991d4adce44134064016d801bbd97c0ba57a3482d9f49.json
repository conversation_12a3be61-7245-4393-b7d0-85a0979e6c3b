{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\ReportCaviPageNew.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport '../../styles/reports.css';\nimport { Box, Typography, Paper, Grid, Card, CardContent, Button, Chip, Alert, CircularProgress, Divider, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, TextField, Accordion, AccordionSummary, AccordionDetails, Switch, FormControlLabel } from '@mui/material';\nimport { Assessment as AssessmentIcon, Timeline as TimelineIcon, List as ListIcon, Download as DownloadIcon, Visibility as VisibilityIcon, Refresh as RefreshIcon, DateRange as DateRangeIcon, Cable as CableIcon, Inventory as InventoryIcon, ExpandMore as ExpandMoreIcon, ShowChart as ShowChartIcon } from '@mui/icons-material';\nimport { useParams } from 'react-router-dom';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport FilterableTable from '../../components/common/FilterableTable';\nimport EmptyState from '../../components/common/EmptyState';\nimport MetricCard from '../../components/common/MetricCard';\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ReportCaviPageNew = () => {\n  _s();\n  const {\n    cantiereId\n  } = useParams();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedReportType, setSelectedReportType] = useState('progress');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobine: null,\n    caviStato: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading progress report:', err);\n          return {\n            content: null\n          };\n        });\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video').catch(err => {\n          console.error('Error loading BOQ report:', err);\n          return {\n            content: null\n          };\n        });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData] = await Promise.all([progressPromise, boqPromise]);\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n      let response;\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(cantiereId, formData.data_inizio, formData.data_fine, format);\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n  const renderProgressReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 2,\n        border: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: '#2c3e50'\n        },\n        children: \"\\uD83D\\uDCCA Report Avanzamento Lavori\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Metri Totali\",\n          value: data.metri_totali,\n          unit: \"m\",\n          subtitle: \"Lunghezza complessiva del progetto\",\n          gradient: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n          size: \"medium\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Metri Posati\",\n          value: data.metri_posati,\n          unit: \"m\",\n          subtitle: `${data.percentuale_avanzamento}% completato`,\n          gradient: \"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\",\n          progress: data.percentuale_avanzamento,\n          trend: data.percentuale_avanzamento > 50 ? 'up' : data.percentuale_avanzamento > 25 ? 'flat' : 'down',\n          trendValue: `${data.percentuale_avanzamento}%`,\n          size: \"medium\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Metri Rimanenti\",\n          value: data.metri_da_posare,\n          unit: \"m\",\n          subtitle: `${(100 - data.percentuale_avanzamento).toFixed(1)}% da completare`,\n          gradient: \"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\",\n          size: \"medium\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Media/Giorno\",\n          value: data.media_giornaliera || 0,\n          unit: \"m\",\n          subtitle: data.giorni_stimati ? `${data.giorni_stimati} giorni lavorativi rimasti` : data.media_giornaliera > 0 ? 'Calcolo in corso' : 'Nessuna posa recente',\n          gradient: \"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\",\n          size: \"medium\",\n          tooltip: data.giorni_lavorativi_effettivi ? `Calcolata su ${data.giorni_lavorativi_effettivi} giorni di lavoro effettivo. Include solo i giorni in cui è stata effettuata posa.` : 'Media giornaliera basata sui giorni di lavoro effettivo'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(ProgressChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            border: '1px solid #e0e0e0'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n                sx: {\n                  color: '#3498db',\n                  mr: 1,\n                  fontSize: 28\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: '#2c3e50'\n                },\n                children: \"Stato Cavi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 6,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center',\n                    p: 2,\n                    bgcolor: '#f8f9fa',\n                    borderRadius: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700,\n                      color: '#2c3e50',\n                      mb: 1\n                    },\n                    children: data.totale_cavi\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: \"Cavi Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 6,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center',\n                    p: 2,\n                    bgcolor: '#e8f5e8',\n                    borderRadius: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700,\n                      color: '#27ae60',\n                      mb: 1\n                    },\n                    children: data.cavi_posati\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: [\"Cavi Posati (\", data.percentuale_cavi, \"%)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Progresso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: [data.percentuale_cavi, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  width: '100%',\n                  height: 8,\n                  bgcolor: '#e0e0e0',\n                  borderRadius: 4,\n                  overflow: 'hidden'\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: `${data.percentuale_cavi}%`,\n                    height: '100%',\n                    bgcolor: '#27ae60',\n                    transition: 'width 0.3s ease'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            border: '1px solid #e0e0e0'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(TimelineIcon, {\n                sx: {\n                  color: '#e74c3c',\n                  mr: 1,\n                  fontSize: 28\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: '#2c3e50'\n                },\n                children: \"Timeline Progetto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                sx: {\n                  fontWeight: 700,\n                  color: '#e74c3c',\n                  mb: 1\n                },\n                children: [data.media_giornaliera || 0, \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666',\n                  mb: 1\n                },\n                children: \"Media Giornaliera\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 17\n              }, this), data.giorni_lavorativi_effettivi && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  color: '#999',\n                  fontSize: '0.75rem'\n                },\n                children: [\"Basata su \", data.giorni_lavorativi_effettivi, \" giorni di lavoro effettivo\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this), data.giorni_stimati ? /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                p: 2,\n                bgcolor: '#fff3cd',\n                borderRadius: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: '#856404',\n                  mb: 0.5\n                },\n                children: data.data_completamento\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#856404'\n                },\n                children: [\"Completamento previsto in \", data.giorni_stimati, \" giorni\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                p: 2,\n                bgcolor: '#f8f9fa',\n                borderRadius: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666'\n                },\n                children: data.media_giornaliera > 0 ? 'Timeline in calcolo...' : 'Necessaria attività di posa per calcolare la timeline'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this), data.posa_recente && data.posa_recente.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        border: '1px solid #e0e0e0'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(DateRangeIcon, {\n            sx: {\n              color: '#9b59b6',\n              mr: 1,\n              fontSize: 28\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 600,\n              color: '#2c3e50'\n            },\n            children: \"\\uD83D\\uDCC8 Attivit\\xE0 Recente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: data.posa_recente.slice(0, 5).map((posa, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2,\n                border: '1px solid #e0e0e0',\n                borderRadius: 2,\n                bgcolor: index === 0 ? '#f0f8ff' : '#fafafa',\n                transition: 'all 0.2s',\n                '&:hover': {\n                  bgcolor: '#f5f5f5',\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666',\n                  mb: 1\n                },\n                children: posa.data\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                sx: {\n                  fontWeight: 700,\n                  color: '#2c3e50'\n                },\n                children: [posa.metri, \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 21\n              }, this), index === 0 && /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"Pi\\xF9 recente\",\n                size: \"small\",\n                sx: {\n                  mt: 1,\n                  bgcolor: '#3498db',\n                  color: 'white',\n                  fontSize: '0.7rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 13\n        }, this), data.posa_recente.length > 5 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Accordion, {\n            children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n              expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 49\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#3498db'\n                },\n                children: [\"Mostra tutti i \", data.posa_recente.length, \" record\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n              children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n                data: data.posa_recente.map(posa => ({\n                  data: posa.data,\n                  metri: `${posa.metri}m`\n                })),\n                columns: [{\n                  field: 'data',\n                  headerName: 'Data',\n                  width: 200\n                }, {\n                  field: 'metri',\n                  headerName: 'Metri Posati',\n                  width: 150,\n                  align: 'right'\n                }],\n                pagination: true,\n                pageSize: 10\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 474,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 424,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 220,\n    columnNumber: 5\n  }, this);\n  const renderBoqReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 2,\n        border: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(ListIcon, {\n        sx: {\n          color: '#8e44ad',\n          mr: 1,\n          fontSize: 28\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: '#2c3e50'\n        },\n        children: \"\\uD83D\\uDCCB Bill of Quantities - Distinta Materiali\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 517,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 507,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(BoqChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 525,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 524,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3,\n        border: '1px solid #e0e0e0'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n            sx: {\n              color: '#e67e22',\n              mr: 1,\n              fontSize: 24\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 600,\n              color: '#2c3e50'\n            },\n            children: \"Cavi per Tipologia\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n          data: data.cavi_per_tipo || [],\n          columns: [{\n            field: 'tipologia',\n            headerName: 'Tipologia',\n            width: 150\n          }, {\n            field: 'sezione',\n            headerName: 'Sezione',\n            width: 100\n          }, {\n            field: 'num_cavi',\n            headerName: 'Cavi',\n            width: 80,\n            align: 'right',\n            dataType: 'number'\n          }, {\n            field: 'metri_teorici',\n            headerName: 'Metri Teorici',\n            width: 120,\n            align: 'right',\n            dataType: 'number',\n            renderCell: row => `${row.metri_teorici}m`\n          }, {\n            field: 'metri_reali',\n            headerName: 'Metri Reali',\n            width: 120,\n            align: 'right',\n            dataType: 'number',\n            renderCell: row => `${row.metri_reali}m`\n          }, {\n            field: 'metri_da_posare',\n            headerName: 'Da Posare',\n            width: 120,\n            align: 'right',\n            dataType: 'number',\n            renderCell: row => `${row.metri_da_posare}m`\n          }],\n          pageSize: 10\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 530,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        border: '1px solid #e0e0e0'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(InventoryIcon, {\n            sx: {\n              color: '#16a085',\n              mr: 1,\n              fontSize: 24\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 600,\n              color: '#2c3e50'\n            },\n            children: \"Bobine Disponibili\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 559,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n          data: data.bobine_per_tipo || [],\n          columns: [{\n            field: 'tipologia',\n            headerName: 'Tipologia',\n            width: 150\n          }, {\n            field: 'sezione',\n            headerName: 'Sezione',\n            width: 100\n          }, {\n            field: 'num_bobine',\n            headerName: 'Bobine',\n            width: 100,\n            align: 'right',\n            dataType: 'number'\n          }, {\n            field: 'metri_disponibili',\n            headerName: 'Metri Disponibili',\n            width: 150,\n            align: 'right',\n            dataType: 'number',\n            renderCell: row => `${row.metri_disponibili}m`\n          }],\n          pageSize: 10\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 558,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 557,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 505,\n    columnNumber: 5\n  }, this);\n  const renderPosaPeriodoReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: 'warning.main'\n        },\n        children: \"Report Posa per Periodo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 589,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 588,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'warning.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [data.totale_metri_periodo, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Metri Totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            children: [data.data_inizio, \" - \", data.data_fine]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 611,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'info.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: data.giorni_attivi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Giorni Attivi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 620,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'success.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [data.media_giornaliera, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Media/Giorno\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 629,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 628,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'primary.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [Math.round(data.totale_metri_periodo / data.giorni_attivi * 7), \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Media/Settimana\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 636,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 610,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(TimelineChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 649,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 648,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          mb: 2,\n          fontWeight: 600\n        },\n        children: \"Dettaglio Posa Giornaliera\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 655,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n        data: data.posa_giornaliera || [],\n        columns: [{\n          field: 'data',\n          headerName: 'Data',\n          width: 200\n        }, {\n          field: 'metri',\n          headerName: 'Metri Posati',\n          width: 150,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri}m`\n        }],\n        pageSize: 10\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 658,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 654,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 586,\n    columnNumber: 5\n  }, this);\n  const renderDialog = () => /*#__PURE__*/_jsxDEV(Dialog, {\n    open: openDialog,\n    onClose: handleCloseDialog,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 675,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 680,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        sx: {\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Formato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: formData.formato,\n              label: \"Formato\",\n              onChange: e => setFormData({\n                ...formData,\n                formato: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"video\",\n                children: \"Visualizza a schermo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"pdf\",\n                children: \"Download PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 695,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"excel\",\n                children: \"Download Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 689,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 686,\n          columnNumber: 11\n        }, this), dialogType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Inizio\",\n              value: formData.data_inizio,\n              onChange: e => setFormData({\n                ...formData,\n                data_inizio: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 706,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 705,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Fine\",\n              value: formData.data_fine,\n              onChange: e => setFormData({\n                ...formData,\n                data_fine: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 715,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 685,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 678,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCloseDialog,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 730,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleGenerateReport,\n        variant: \"contained\",\n        disabled: loading,\n        startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 735,\n          columnNumber: 32\n        }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 735,\n          columnNumber: 65\n        }, this),\n        children: loading ? 'Generazione...' : 'Genera Report'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 731,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 729,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 674,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"report-main-container report-fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'flex-end',\n        alignItems: 'center',\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 747,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 746,\n      columnNumber: 7\n    }, this), loading && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 753,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 752,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600,\n            color: '#2c3e50',\n            mb: 2,\n            textAlign: 'center'\n          },\n          children: \"\\uD83C\\uDFAF Seleziona il tipo di report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 761,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: `report-card ${selectedReportType === 'progress' ? 'report-card-selected' : ''}`,\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'progress' ? '2px solid #3498db' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'progress' ? '#f0f8ff' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('progress'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#3498db',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 779,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Avanzamento\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 780,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Panoramica lavori\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 783,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 767,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 766,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'boq' ? '2px solid #8e44ad' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'boq' ? '#f8f4ff' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('boq'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(ListIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#8e44ad',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 803,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Bill of Quantities\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 804,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Distinta materiali\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 807,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 802,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 792,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 791,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'posa-periodo' ? '2px solid #9b59b6' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'posa-periodo' ? '#f8f4ff' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('posa-periodo'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TimelineIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#9b59b6',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 829,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Posa per Periodo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 830,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Analisi temporale\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 833,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 818,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 817,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 764,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 760,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          minHeight: '400px'\n        },\n        children: [selectedReportType === 'progress' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.progress ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 851,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('progress', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 850,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 861,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('progress', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 860,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 849,\n              columnNumber: 19\n            }, this), renderProgressReport(reportsData.progress)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 848,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"loading\",\n            reportType: \"progress\",\n            title: \"Caricamento Report Avanzamento...\",\n            description: \"Stiamo elaborando i dati dell'avanzamento dei lavori\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 873,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"error\",\n            reportType: \"progress\",\n            title: \"Errore nel caricamento\",\n            description: \"Impossibile caricare il report di avanzamento. Verifica la connessione e riprova.\",\n            onRetry: () => {\n              setLoading(true);\n              reportService.getProgressReport(cantiereId, 'video').then(data => {\n                setReportsData(prev => ({\n                  ...prev,\n                  progress: data.content\n                }));\n              }).catch(err => {\n                console.error('Error retrying progress report:', err);\n              }).finally(() => {\n                setLoading(false);\n              });\n            },\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 880,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 846,\n          columnNumber: 13\n        }, this), selectedReportType === 'boq' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.boq ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 914,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 913,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 924,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 923,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 912,\n              columnNumber: 19\n            }, this), renderBoqReport(reportsData.boq)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 911,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"loading\",\n            reportType: \"boq\",\n            title: \"Caricamento Bill of Quantities...\",\n            description: \"Stiamo elaborando la distinta materiali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 936,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"error\",\n            reportType: \"boq\",\n            title: \"Errore nel caricamento\",\n            description: \"Impossibile caricare la distinta materiali. Verifica la connessione e riprova.\",\n            onRetry: () => {\n              setLoading(true);\n              reportService.getBillOfQuantities(cantiereId, 'video').then(data => {\n                setReportsData(prev => ({\n                  ...prev,\n                  boq: data.content\n                }));\n              }).catch(err => {\n                console.error('Error retrying BOQ report:', err);\n              }).finally(() => {\n                setLoading(false);\n              });\n            },\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 943,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 909,\n          columnNumber: 13\n        }, this), selectedReportType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.posaPeriodo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 979,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 978,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 989,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 988,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 977,\n              columnNumber: 19\n            }, this), renderPosaPeriodoReport(reportsData.posaPeriodo)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 976,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"action-required\",\n            reportType: \"posa-periodo\",\n            title: \"Seleziona un Periodo\",\n            description: \"Scegli un intervallo di date per analizzare i trend temporali, pattern di lavoro e produttivit\\xE0 del team.\",\n            actionLabel: \"Seleziona Periodo\",\n            onAction: () => {\n              setDialogType('posa-periodo');\n              // Set default date range (last month to today)\n              const today = new Date();\n              const lastMonth = new Date();\n              lastMonth.setMonth(today.getMonth() - 1);\n              setFormData({\n                ...formData,\n                data_inizio: lastMonth.toISOString().split('T')[0],\n                data_fine: today.toISOString().split('T')[0]\n              });\n              setOpenDialog(true);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1001,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 974,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 843,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 758,\n      columnNumber: 7\n    }, this), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 744,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportCaviPageNew, \"BCUld4W6mK1poP57jh1tXjAZaVo=\", false, function () {\n  return [useParams];\n});\n_c = ReportCaviPageNew;\nexport default ReportCaviPageNew;\nvar _c;\n$RefreshReg$(_c, \"ReportCaviPageNew\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "CircularProgress", "Divider", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "Accordion", "AccordionSummary", "AccordionDetails", "Switch", "FormControlLabel", "Assessment", "AssessmentIcon", "Timeline", "TimelineIcon", "List", "ListIcon", "Download", "DownloadIcon", "Visibility", "VisibilityIcon", "Refresh", "RefreshIcon", "DateRange", "DateRangeIcon", "Cable", "CableIcon", "Inventory", "InventoryIcon", "ExpandMore", "ExpandMoreIcon", "ShowChart", "ShowChartIcon", "useParams", "AdminHomeButton", "reportService", "FilterableTable", "EmptyState", "MetricCard", "ProgressChart", "<PERSON><PERSON><PERSON><PERSON>", "TimelineChart", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ReportCaviPageNew", "_s", "cantiereId", "loading", "setLoading", "error", "setError", "reportData", "setReportData", "selectedReport", "setSelectedReport", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selectedReportType", "setSelectedReportType", "formData", "setFormData", "formato", "data_inizio", "data_fine", "id_bobina", "reportsData", "setReportsData", "progress", "boq", "bobine", "caviStato", "bobinaSpecifica", "posaPeriodo", "show<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadAllReports", "progressPromise", "getProgressReport", "catch", "err", "console", "content", "boq<PERSON><PERSON><PERSON>", "getBillOfQuantities", "progressData", "boqData", "Promise", "all", "generateReportWithFormat", "reportType", "format", "response", "getPosaPerPeriodoReport", "Error", "prev", "file_url", "window", "open", "detail", "message", "handleGenerateReport", "handleCloseDialog", "renderProgressReport", "data", "children", "sx", "display", "justifyContent", "alignItems", "mb", "p", "bgcolor", "borderRadius", "border", "variant", "fontWeight", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "control", "checked", "onChange", "e", "target", "label", "mr", "container", "spacing", "item", "xs", "sm", "md", "title", "value", "metri_totali", "unit", "subtitle", "gradient", "size", "metri_posati", "percentuale_avanzamento", "trend", "trendValue", "metri_da_posare", "toFixed", "media_giornaliera", "giorni_stimati", "tooltip", "giorni_lavorativi_effettivi", "height", "fontSize", "textAlign", "totale_cavi", "cavi_posati", "percentuale_cavi", "mt", "width", "overflow", "transition", "data_completamento", "posa_recente", "length", "slice", "map", "posa", "index", "transform", "boxShadow", "metri", "expandIcon", "columns", "field", "headerName", "align", "pagination", "pageSize", "renderBoqReport", "cavi_per_tipo", "dataType", "renderCell", "row", "metri_te<PERSON>ci", "metri_reali", "bobine_per_tipo", "metri_disponibili", "renderPosaPeriodoReport", "totale_metri_periodo", "giorni_attivi", "Math", "round", "posa_giornal<PERSON>", "renderDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "severity", "type", "InputLabelProps", "shrink", "onClick", "disabled", "startIcon", "className", "my", "cursor", "flexDirection", "minHeight", "description", "onRetry", "then", "finally", "actionLabel", "onAction", "today", "Date", "lastM<PERSON>h", "setMonth", "getMonth", "toISOString", "split", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/ReportCaviPageNew.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport '../../styles/reports.css';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n\n  Button,\n  Chip,\n  Alert,\n  CircularProgress,\n  Divider,\n\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Switch,\n  FormControlLabel\n} from '@mui/material';\nimport {\n  Assessment as AssessmentIcon,\n\n  Timeline as TimelineIcon,\n  List as ListIcon,\n  Download as DownloadIcon,\n  Visibility as VisibilityIcon,\n  Refresh as RefreshIcon,\n\n  DateRange as DateRangeIcon,\n  Cable as CableIcon,\n  Inventory as InventoryIcon,\n  ExpandMore as ExpandMoreIcon,\n  ShowChart as ShowChartIcon\n} from '@mui/icons-material';\nimport { useParams } from 'react-router-dom';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport FilterableTable from '../../components/common/FilterableTable';\nimport EmptyState from '../../components/common/EmptyState';\nimport MetricCard from '../../components/common/MetricCard';\n\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\n\nconst ReportCaviPageNew = () => {\n  const { cantiereId } = useParams();\n\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedReportType, setSelectedReportType] = useState('progress');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobine: null,\n    caviStato: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading progress report:', err);\n            return { content: null };\n          });\n\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading BOQ report:', err);\n            return { content: null };\n          });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData] = await Promise.all([\n          progressPromise,\n          boqPromise\n        ]);\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      let response;\n\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(\n            cantiereId,\n            formData.data_inizio,\n            formData.data_fine,\n            format\n          );\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n\n\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n\n\n\n  const renderProgressReport = (data) => (\n    <Box>\n      {/* Header con controlli migliorato */}\n      <Box sx={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 2,\n        border: '1px solid #e0e0e0'\n      }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n          📊 Report Avanzamento Lavori\n        </Typography>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Metriche Principali - Cards Moderne con MetricCard */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Metri Totali\"\n            value={data.metri_totali}\n            unit=\"m\"\n            subtitle=\"Lunghezza complessiva del progetto\"\n            gradient=\"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\"\n            size=\"medium\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Metri Posati\"\n            value={data.metri_posati}\n            unit=\"m\"\n            subtitle={`${data.percentuale_avanzamento}% completato`}\n            gradient=\"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\"\n            progress={data.percentuale_avanzamento}\n            trend={data.percentuale_avanzamento > 50 ? 'up' : data.percentuale_avanzamento > 25 ? 'flat' : 'down'}\n            trendValue={`${data.percentuale_avanzamento}%`}\n            size=\"medium\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Metri Rimanenti\"\n            value={data.metri_da_posare}\n            unit=\"m\"\n            subtitle={`${(100 - data.percentuale_avanzamento).toFixed(1)}% da completare`}\n            gradient=\"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\"\n            size=\"medium\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Media/Giorno\"\n            value={data.media_giornaliera || 0}\n            unit=\"m\"\n            subtitle={\n              data.giorni_stimati\n                ? `${data.giorni_stimati} giorni lavorativi rimasti`\n                : (data.media_giornaliera > 0\n                    ? 'Calcolo in corso'\n                    : 'Nessuna posa recente')\n            }\n            gradient=\"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\"\n            size=\"medium\"\n            tooltip={\n              data.giorni_lavorativi_effettivi\n                ? `Calcolata su ${data.giorni_lavorativi_effettivi} giorni di lavoro effettivo. Include solo i giorni in cui è stata effettuata posa.`\n                : 'Media giornaliera basata sui giorni di lavoro effettivo'\n            }\n          />\n        </Grid>\n      </Grid>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Box sx={{ mb: 4 }}>\n          <ProgressChart data={data} />\n        </Box>\n      )}\n\n      {/* Dettagli Performance - Cards Informative */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} md={6}>\n          <Card sx={{ height: '100%', border: '1px solid #e0e0e0' }}>\n            <CardContent sx={{ p: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <CableIcon sx={{ color: '#3498db', mr: 1, fontSize: 28 }} />\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                  Stato Cavi\n                </Typography>\n              </Box>\n              <Grid container spacing={2}>\n                <Grid item xs={6}>\n                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>\n                      {data.totale_cavi}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                      Cavi Totali\n                    </Typography>\n                  </Box>\n                </Grid>\n                <Grid item xs={6}>\n                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#e8f5e8', borderRadius: 1 }}>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#27ae60', mb: 1 }}>\n                      {data.cavi_posati}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                      Cavi Posati ({data.percentuale_cavi}%)\n                    </Typography>\n                  </Box>\n                </Grid>\n              </Grid>\n              <Box sx={{ mt: 2 }}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body2\">Progresso</Typography>\n                  <Typography variant=\"body2\" sx={{ fontWeight: 600 }}>\n                    {data.percentuale_cavi}%\n                  </Typography>\n                </Box>\n                <Box sx={{\n                  width: '100%',\n                  height: 8,\n                  bgcolor: '#e0e0e0',\n                  borderRadius: 4,\n                  overflow: 'hidden'\n                }}>\n                  <Box sx={{\n                    width: `${data.percentuale_cavi}%`,\n                    height: '100%',\n                    bgcolor: '#27ae60',\n                    transition: 'width 0.3s ease'\n                  }} />\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <Card sx={{ height: '100%', border: '1px solid #e0e0e0' }}>\n            <CardContent sx={{ p: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <TimelineIcon sx={{ color: '#e74c3c', mr: 1, fontSize: 28 }} />\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                  Timeline Progetto\n                </Typography>\n              </Box>\n              <Box sx={{ textAlign: 'center', mb: 2 }}>\n                <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#e74c3c', mb: 1 }}>\n                  {data.media_giornaliera || 0}m\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: '#666', mb: 1 }}>\n                  Media Giornaliera\n                </Typography>\n                {data.giorni_lavorativi_effettivi && (\n                  <Typography variant=\"caption\" sx={{ color: '#999', fontSize: '0.75rem' }}>\n                    Basata su {data.giorni_lavorativi_effettivi} giorni di lavoro effettivo\n                  </Typography>\n                )}\n              </Box>\n              {data.giorni_stimati ? (\n                <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#fff3cd', borderRadius: 1 }}>\n                  <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#856404', mb: 0.5 }}>\n                    {data.data_completamento}\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#856404' }}>\n                    Completamento previsto in {data.giorni_stimati} giorni\n                  </Typography>\n                </Box>\n              ) : (\n                <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>\n                  <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                    {data.media_giornaliera > 0 ? 'Timeline in calcolo...' : 'Necessaria attività di posa per calcolare la timeline'}\n                  </Typography>\n                </Box>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Attività Recente - Design Migliorato */}\n      {data.posa_recente && data.posa_recente.length > 0 && (\n        <Card sx={{ border: '1px solid #e0e0e0' }}>\n          <CardContent sx={{ p: 3 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n              <DateRangeIcon sx={{ color: '#9b59b6', mr: 1, fontSize: 28 }} />\n              <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                📈 Attività Recente\n              </Typography>\n            </Box>\n\n            {/* Mostra solo gli ultimi 5 record in formato card per mobile-friendly */}\n            <Grid container spacing={2}>\n              {data.posa_recente.slice(0, 5).map((posa, index) => (\n                <Grid item xs={12} sm={6} md={4} key={index}>\n                  <Box sx={{\n                    p: 2,\n                    border: '1px solid #e0e0e0',\n                    borderRadius: 2,\n                    bgcolor: index === 0 ? '#f0f8ff' : '#fafafa',\n                    transition: 'all 0.2s',\n                    '&:hover': {\n                      bgcolor: '#f5f5f5',\n                      transform: 'translateY(-2px)',\n                      boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n                    }\n                  }}>\n                    <Typography variant=\"body2\" sx={{ color: '#666', mb: 1 }}>\n                      {posa.data}\n                    </Typography>\n                    <Typography variant=\"h5\" sx={{ fontWeight: 700, color: '#2c3e50' }}>\n                      {posa.metri}m\n                    </Typography>\n                    {index === 0 && (\n                      <Chip\n                        label=\"Più recente\"\n                        size=\"small\"\n                        sx={{\n                          mt: 1,\n                          bgcolor: '#3498db',\n                          color: 'white',\n                          fontSize: '0.7rem'\n                        }}\n                      />\n                    )}\n                  </Box>\n                </Grid>\n              ))}\n            </Grid>\n\n            {/* Link per vedere tutti i dati se ce ne sono di più */}\n            {data.posa_recente.length > 5 && (\n              <Box sx={{ mt: 3, textAlign: 'center' }}>\n                <Accordion>\n                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n                    <Typography variant=\"body2\" sx={{ color: '#3498db' }}>\n                      Mostra tutti i {data.posa_recente.length} record\n                    </Typography>\n                  </AccordionSummary>\n                  <AccordionDetails>\n                    <FilterableTable\n                      data={data.posa_recente.map(posa => ({\n                        data: posa.data,\n                        metri: `${posa.metri}m`\n                      }))}\n                      columns={[\n                        { field: 'data', headerName: 'Data', width: 200 },\n                        { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right' }\n                      ]}\n                      pagination={true}\n                      pageSize={10}\n                    />\n                  </AccordionDetails>\n                </Accordion>\n              </Box>\n            )}\n          </CardContent>\n        </Card>\n      )}\n    </Box>\n  );\n\n  const renderBoqReport = (data) => (\n    <Box>\n      {/* Header migliorato */}\n      <Box sx={{\n        display: 'flex',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 2,\n        border: '1px solid #e0e0e0'\n      }}>\n        <ListIcon sx={{ color: '#8e44ad', mr: 1, fontSize: 28 }} />\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n          📋 Bill of Quantities - Distinta Materiali\n        </Typography>\n      </Box>\n\n      {/* Grafici BOQ se disponibili */}\n      {showCharts && (\n        <Box sx={{ mb: 4 }}>\n          <BoqChart data={data} />\n        </Box>\n      )}\n\n      {/* Cavi per Tipologia - Design migliorato */}\n      <Card sx={{ mb: 3, border: '1px solid #e0e0e0' }}>\n        <CardContent sx={{ p: 3 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n            <CableIcon sx={{ color: '#e67e22', mr: 1, fontSize: 24 }} />\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n              Cavi per Tipologia\n            </Typography>\n          </Box>\n          <FilterableTable\n            data={data.cavi_per_tipo || []}\n            columns={[\n              { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n              { field: 'sezione', headerName: 'Sezione', width: 100 },\n              { field: 'num_cavi', headerName: 'Cavi', width: 80, align: 'right', dataType: 'number' },\n              { field: 'metri_teorici', headerName: 'Metri Teorici', width: 120, align: 'right', dataType: 'number',\n                renderCell: (row) => `${row.metri_teorici}m` },\n              { field: 'metri_reali', headerName: 'Metri Reali', width: 120, align: 'right', dataType: 'number',\n                renderCell: (row) => `${row.metri_reali}m` },\n              { field: 'metri_da_posare', headerName: 'Da Posare', width: 120, align: 'right', dataType: 'number',\n                renderCell: (row) => `${row.metri_da_posare}m` }\n            ]}\n            pageSize={10}\n          />\n        </CardContent>\n      </Card>\n\n      {/* Bobine Disponibili - Design migliorato */}\n      <Card sx={{ border: '1px solid #e0e0e0' }}>\n        <CardContent sx={{ p: 3 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n            <InventoryIcon sx={{ color: '#16a085', mr: 1, fontSize: 24 }} />\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n              Bobine Disponibili\n            </Typography>\n          </Box>\n          <FilterableTable\n            data={data.bobine_per_tipo || []}\n            columns={[\n              { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n              { field: 'sezione', headerName: 'Sezione', width: 100 },\n              { field: 'num_bobine', headerName: 'Bobine', width: 100, align: 'right', dataType: 'number' },\n              { field: 'metri_disponibili', headerName: 'Metri Disponibili', width: 150, align: 'right', dataType: 'number',\n                renderCell: (row) => `${row.metri_disponibili}m` }\n            ]}\n            pageSize={10}\n          />\n        </CardContent>\n      </Card>\n    </Box>\n  );\n\n\n\n\n\n  const renderPosaPeriodoReport = (data) => (\n    <Box>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: 'warning.main' }}>\n          Report Posa per Periodo\n        </Typography>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Statistiche Periodo - Layout Orizzontale */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'warning.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.totale_metri_periodo}m\n            </Typography>\n            <Typography variant=\"body1\">Metri Totali</Typography>\n            <Typography variant=\"caption\">{data.data_inizio} - {data.data_fine}</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'info.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.giorni_attivi}\n            </Typography>\n            <Typography variant=\"body1\">Giorni Attivi</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'success.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.media_giornaliera}m\n            </Typography>\n            <Typography variant=\"body1\">Media/Giorno</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'primary.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {Math.round(data.totale_metri_periodo / data.giorni_attivi * 7)}m\n            </Typography>\n            <Typography variant=\"body1\">Media/Settimana</Typography>\n          </Paper>\n        </Grid>\n      </Grid>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Box sx={{ mb: 4 }}>\n          <TimelineChart data={data} />\n        </Box>\n      )}\n\n      {/* Posa Giornaliera */}\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n          Dettaglio Posa Giornaliera\n        </Typography>\n        <FilterableTable\n          data={data.posa_giornaliera || []}\n          columns={[\n            { field: 'data', headerName: 'Data', width: 200 },\n            { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri}m` }\n          ]}\n          pageSize={10}\n        />\n      </Paper>\n    </Box>\n  );\n\n\n\n  const renderDialog = () => (\n    <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n      <DialogTitle>\n        {selectedReport?.title}\n      </DialogTitle>\n      <DialogContent>\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n\n        <Grid container spacing={2} sx={{ mt: 1 }}>\n          <Grid item xs={12}>\n            <FormControl fullWidth>\n              <InputLabel>Formato</InputLabel>\n              <Select\n                value={formData.formato}\n                label=\"Formato\"\n                onChange={(e) => setFormData({ ...formData, formato: e.target.value })}\n              >\n                <MenuItem value=\"video\">Visualizza a schermo</MenuItem>\n                <MenuItem value=\"pdf\">Download PDF</MenuItem>\n                <MenuItem value=\"excel\">Download Excel</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n\n\n\n          {dialogType === 'posa-periodo' && (\n            <>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Inizio\"\n                  value={formData.data_inizio}\n                  onChange={(e) => setFormData({ ...formData, data_inizio: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Fine\"\n                  value={formData.data_fine}\n                  onChange={(e) => setFormData({ ...formData, data_fine: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n        </Grid>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={handleCloseDialog}>Annulla</Button>\n        <Button\n          onClick={handleGenerateReport}\n          variant=\"contained\"\n          disabled={loading}\n          startIcon={loading ? <CircularProgress size={20} /> : <VisibilityIcon />}\n        >\n          {loading ? 'Generazione...' : 'Genera Report'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  return (\n    <Box className=\"report-main-container report-fade-in\">\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2 }}>\n        <AdminHomeButton />\n      </Box>\n\n      {/* Loading indicator */}\n      {loading && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      )}\n\n      {/* Reports Navigation */}\n      <Box sx={{ mt: 3 }}>\n        {/* Report Navigation - Design Compatto */}\n        <Box sx={{ mb: 3 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50', mb: 2, textAlign: 'center' }}>\n            🎯 Seleziona il tipo di report\n          </Typography>\n          <Grid container spacing={2}>\n            {/* Report Avanzamento */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                className={`report-card ${selectedReportType === 'progress' ? 'report-card-selected' : ''}`}\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'progress' ? '2px solid #3498db' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'progress' ? '#f0f8ff' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('progress')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <AssessmentIcon sx={{ fontSize: 32, color: '#3498db', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Avanzamento\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Panoramica lavori\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            {/* Bill of Quantities */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'boq' ? '2px solid #8e44ad' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'boq' ? '#f8f4ff' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('boq')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <ListIcon sx={{ fontSize: 32, color: '#8e44ad', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Bill of Quantities\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Distinta materiali\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n\n\n            {/* Posa per Periodo */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'posa-periodo' ? '2px solid #9b59b6' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'posa-periodo' ? '#f8f4ff' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('posa-periodo')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <TimelineIcon sx={{ fontSize: 32, color: '#9b59b6', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Posa per Periodo\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Analisi temporale\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        </Box>\n\n        {/* Report Content */}\n        <Box sx={{ minHeight: '400px' }}>\n          {/* Progress Report */}\n          {selectedReportType === 'progress' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.progress ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderProgressReport(reportsData.progress)}\n                </Box>\n              ) : loading ? (\n                <EmptyState\n                  type=\"loading\"\n                  reportType=\"progress\"\n                  title=\"Caricamento Report Avanzamento...\"\n                  description=\"Stiamo elaborando i dati dell'avanzamento dei lavori\"\n                />\n              ) : (\n                <EmptyState\n                  type=\"error\"\n                  reportType=\"progress\"\n                  title=\"Errore nel caricamento\"\n                  description=\"Impossibile caricare il report di avanzamento. Verifica la connessione e riprova.\"\n                  onRetry={() => {\n                    setLoading(true);\n                    reportService.getProgressReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          progress: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying progress report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                  loading={loading}\n                />\n              )}\n            </Paper>\n          )}\n\n          {/* Bill of Quantities */}\n          {selectedReportType === 'boq' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.boq ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('boq', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('boq', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderBoqReport(reportsData.boq)}\n                </Box>\n              ) : loading ? (\n                <EmptyState\n                  type=\"loading\"\n                  reportType=\"boq\"\n                  title=\"Caricamento Bill of Quantities...\"\n                  description=\"Stiamo elaborando la distinta materiali\"\n                />\n              ) : (\n                <EmptyState\n                  type=\"error\"\n                  reportType=\"boq\"\n                  title=\"Errore nel caricamento\"\n                  description=\"Impossibile caricare la distinta materiali. Verifica la connessione e riprova.\"\n                  onRetry={() => {\n                    setLoading(true);\n                    reportService.getBillOfQuantities(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          boq: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying BOQ report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                  loading={loading}\n                />\n              )}\n            </Paper>\n          )}\n\n\n\n          {/* Posa per Periodo Report */}\n          {selectedReportType === 'posa-periodo' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.posaPeriodo ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('posa-periodo', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('posa-periodo', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderPosaPeriodoReport(reportsData.posaPeriodo)}\n                </Box>\n              ) : (\n                <EmptyState\n                  type=\"action-required\"\n                  reportType=\"posa-periodo\"\n                  title=\"Seleziona un Periodo\"\n                  description=\"Scegli un intervallo di date per analizzare i trend temporali, pattern di lavoro e produttività del team.\"\n                  actionLabel=\"Seleziona Periodo\"\n                  onAction={() => {\n                    setDialogType('posa-periodo');\n                    // Set default date range (last month to today)\n                    const today = new Date();\n                    const lastMonth = new Date();\n                    lastMonth.setMonth(today.getMonth() - 1);\n\n                    setFormData({\n                      ...formData,\n                      data_inizio: lastMonth.toISOString().split('T')[0],\n                      data_fine: today.toISOString().split('T')[0]\n                    });\n                    setOpenDialog(true);\n                  }}\n                />\n              )}\n            </Paper>\n          )}\n        </Box>\n      </Box>\n\n      {/* Dialog per configurazione report */}\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default ReportCaviPageNew;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,0BAA0B;AACjC,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EAEXC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EAEPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,MAAM,EACNC,gBAAgB,QACX,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAE5BC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,OAAO,IAAIC,WAAW,EAEtBC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,SAAS,EAClBC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,EAC5BC,SAAS,IAAIC,aAAa,QACrB,qBAAqB;AAC5B,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,UAAU,MAAM,oCAAoC;;AAG3D;AACA,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,QAAQ,MAAM,kCAAkC;AACvD,OAAOC,aAAa,MAAM,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAW,CAAC,GAAGf,SAAS,CAAC,CAAC;EAElC,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmE,KAAK,EAAEC,QAAQ,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqE,UAAU,EAAEC,aAAa,CAAC,GAAGtE,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACuE,cAAc,EAAEC,iBAAiB,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACyE,UAAU,EAAEC,aAAa,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2E,UAAU,EAAEC,aAAa,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6E,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9E,QAAQ,CAAC,UAAU,CAAC;EACxE,MAAM,CAAC+E,QAAQ,EAAEC,WAAW,CAAC,GAAGhF,QAAQ,CAAC;IACvCiF,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtF,QAAQ,CAAC;IAC7CuF,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACfC,eAAe,EAAE,IAAI;IACrBC,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG9F,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM8F,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC7B,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF;QACA,MAAM8B,eAAe,GAAG7C,aAAa,CAAC8C,iBAAiB,CAACjC,UAAU,EAAE,OAAO,CAAC,CACzEkC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAACjC,KAAK,CAAC,gCAAgC,EAAEgC,GAAG,CAAC;UACpD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMC,UAAU,GAAGnD,aAAa,CAACoD,mBAAmB,CAACvC,UAAU,EAAE,OAAO,CAAC,CACtEkC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAACjC,KAAK,CAAC,2BAA2B,EAAEgC,GAAG,CAAC;UAC/C,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;;QAEJ;QACA,MAAM,CAACG,YAAY,EAAEC,OAAO,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChDX,eAAe,EACfM,UAAU,CACX,CAAC;;QAEF;QACAhB,cAAc,CAAC;UACbC,QAAQ,EAAEiB,YAAY,CAACH,OAAO;UAC9Bb,GAAG,EAAEiB,OAAO,CAACJ,OAAO;UACpBV,eAAe,EAAE,IAAI;UACrBC,WAAW,EAAE;QACf,CAAC,CAAC;;QAEF;QACA,IAAIY,YAAY,CAACH,OAAO,IAAII,OAAO,CAACJ,OAAO,EAAE;UAC3CjC,QAAQ,CAAC,IAAI,CAAC;QAChB,CAAC,MAAM;UACLA,QAAQ,CAAC,uDAAuD,CAAC;QACnE;MACF,CAAC,CAAC,OAAO+B,GAAG,EAAE;QACZ;QACAC,OAAO,CAACjC,KAAK,CAAC,mCAAmC,EAAEgC,GAAG,CAAC;QACvD/B,QAAQ,CAAC,uDAAuD,CAAC;MACnE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIF,UAAU,EAAE;MACd+B,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC/B,UAAU,CAAC,CAAC;;EAIhB;EACA,MAAM4C,wBAAwB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,MAAM,KAAK;IAC7D,IAAI;MACF5C,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI2C,QAAQ;MAEZ,QAAQF,UAAU;QAChB,KAAK,UAAU;UACbE,QAAQ,GAAG,MAAM5D,aAAa,CAAC8C,iBAAiB,CAACjC,UAAU,EAAE8C,MAAM,CAAC;UACpE;QACF,KAAK,KAAK;UACRC,QAAQ,GAAG,MAAM5D,aAAa,CAACoD,mBAAmB,CAACvC,UAAU,EAAE8C,MAAM,CAAC;UACtE;QAEF,KAAK,cAAc;UACjB,IAAI,CAAC/B,QAAQ,CAACG,WAAW,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;YAChDf,QAAQ,CAAC,4CAA4C,CAAC;YACtD;UACF;UACA2C,QAAQ,GAAG,MAAM5D,aAAa,CAAC6D,uBAAuB,CACpDhD,UAAU,EACVe,QAAQ,CAACG,WAAW,EACpBH,QAAQ,CAACI,SAAS,EAClB2B,MACF,CAAC;UACD;QACF;UACE,MAAM,IAAIG,KAAK,CAAC,iCAAiC,CAAC;MACtD;MAEA,IAAIH,MAAM,KAAK,OAAO,EAAE;QACtB;QACA,IAAID,UAAU,KAAK,kBAAkB,IAAIA,UAAU,KAAK,cAAc,EAAE;UACtEvB,cAAc,CAAC4B,IAAI,KAAK;YACtB,GAAGA,IAAI;YACP,CAACL,UAAU,KAAK,kBAAkB,GAAG,iBAAiB,GAAG,aAAa,GAAGE,QAAQ,CAACV;UACpF,CAAC,CAAC,CAAC;QACL;QACA/B,aAAa,CAACyC,QAAQ,CAACV,OAAO,CAAC;MACjC,CAAC,MAAM;QACL;QACA,IAAIU,QAAQ,CAACI,QAAQ,EAAE;UACrBC,MAAM,CAACC,IAAI,CAACN,QAAQ,CAACI,QAAQ,EAAE,QAAQ,CAAC;QAC1C;MACF;IACF,CAAC,CAAC,OAAOhB,GAAG,EAAE;MACZC,OAAO,CAACjC,KAAK,CAAC,sCAAsC,EAAEgC,GAAG,CAAC;MAC1D/B,QAAQ,CAAC+B,GAAG,CAACmB,MAAM,IAAInB,GAAG,CAACoB,OAAO,IAAI,0CAA0C,CAAC;IACnF,CAAC,SAAS;MACRrD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAID,MAAMsD,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,MAAMZ,wBAAwB,CAACjC,UAAU,EAAEI,QAAQ,CAACE,OAAO,CAAC;IAC5DP,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAM+C,iBAAiB,GAAGA,CAAA,KAAM;IAC9B/C,aAAa,CAAC,KAAK,CAAC;IACpBN,QAAQ,CAAC,IAAI,CAAC;IACdY,WAAW,CAAC;MACVC,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAID,MAAMsC,oBAAoB,GAAIC,IAAI,iBAChChE,OAAA,CAACzD,GAAG;IAAA0H,QAAA,gBAEFjE,OAAA,CAACzD,GAAG;MAAC2H,EAAE,EAAE;QACPC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,EAAE,EAAE,CAAC;QACLC,CAAC,EAAE,CAAC;QACJC,OAAO,EAAE,SAAS;QAClBC,YAAY,EAAE,CAAC;QACfC,MAAM,EAAE;MACV,CAAE;MAAAT,QAAA,gBACAjE,OAAA,CAACxD,UAAU;QAACmI,OAAO,EAAC,IAAI;QAACT,EAAE,EAAE;UAAEU,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAU,CAAE;QAAAZ,QAAA,EAAC;MAEpE;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjF,OAAA,CAACjC,gBAAgB;QACfmH,OAAO,eACLlF,OAAA,CAAClC,MAAM;UACLqH,OAAO,EAAEjD,UAAW;UACpBkD,QAAQ,EAAGC,CAAC,IAAKlD,aAAa,CAACkD,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDN,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDM,KAAK,eACHvF,OAAA,CAACzD,GAAG;UAAC2H,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAJ,QAAA,gBACjDjE,OAAA,CAACX,aAAa;YAAC6E,EAAE,EAAE;cAAEsB,EAAE,EAAE;YAAE;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNjF,OAAA,CAACtD,IAAI;MAAC+I,SAAS;MAACC,OAAO,EAAE,CAAE;MAACxB,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACxCjE,OAAA,CAACtD,IAAI;QAACiJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eAC9BjE,OAAA,CAACL,UAAU;UACToG,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAEhC,IAAI,CAACiC,YAAa;UACzBC,IAAI,EAAC,GAAG;UACRC,QAAQ,EAAC,oCAAoC;UAC7CC,QAAQ,EAAC,mDAAmD;UAC5DC,IAAI,EAAC;QAAQ;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPjF,OAAA,CAACtD,IAAI;QAACiJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eAC9BjE,OAAA,CAACL,UAAU;UACToG,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAEhC,IAAI,CAACsC,YAAa;UACzBJ,IAAI,EAAC,GAAG;UACRC,QAAQ,EAAE,GAAGnC,IAAI,CAACuC,uBAAuB,cAAe;UACxDH,QAAQ,EAAC,mDAAmD;UAC5DxE,QAAQ,EAAEoC,IAAI,CAACuC,uBAAwB;UACvCC,KAAK,EAAExC,IAAI,CAACuC,uBAAuB,GAAG,EAAE,GAAG,IAAI,GAAGvC,IAAI,CAACuC,uBAAuB,GAAG,EAAE,GAAG,MAAM,GAAG,MAAO;UACtGE,UAAU,EAAE,GAAGzC,IAAI,CAACuC,uBAAuB,GAAI;UAC/CF,IAAI,EAAC;QAAQ;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPjF,OAAA,CAACtD,IAAI;QAACiJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eAC9BjE,OAAA,CAACL,UAAU;UACToG,KAAK,EAAC,iBAAiB;UACvBC,KAAK,EAAEhC,IAAI,CAAC0C,eAAgB;UAC5BR,IAAI,EAAC,GAAG;UACRC,QAAQ,EAAE,GAAG,CAAC,GAAG,GAAGnC,IAAI,CAACuC,uBAAuB,EAAEI,OAAO,CAAC,CAAC,CAAC,iBAAkB;UAC9EP,QAAQ,EAAC,mDAAmD;UAC5DC,IAAI,EAAC;QAAQ;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPjF,OAAA,CAACtD,IAAI;QAACiJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eAC9BjE,OAAA,CAACL,UAAU;UACToG,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAEhC,IAAI,CAAC4C,iBAAiB,IAAI,CAAE;UACnCV,IAAI,EAAC,GAAG;UACRC,QAAQ,EACNnC,IAAI,CAAC6C,cAAc,GACf,GAAG7C,IAAI,CAAC6C,cAAc,4BAA4B,GACjD7C,IAAI,CAAC4C,iBAAiB,GAAG,CAAC,GACvB,kBAAkB,GAClB,sBACT;UACDR,QAAQ,EAAC,mDAAmD;UAC5DC,IAAI,EAAC,QAAQ;UACbS,OAAO,EACL9C,IAAI,CAAC+C,2BAA2B,GAC5B,gBAAgB/C,IAAI,CAAC+C,2BAA2B,oFAAoF,GACpI;QACL;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGN/C,UAAU,iBACTlC,OAAA,CAACzD,GAAG;MAAC2H,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,eACjBjE,OAAA,CAACJ,aAAa;QAACoE,IAAI,EAAEA;MAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGDjF,OAAA,CAACtD,IAAI;MAAC+I,SAAS;MAACC,OAAO,EAAE,CAAE;MAACxB,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACxCjE,OAAA,CAACtD,IAAI;QAACiJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvBjE,OAAA,CAACrD,IAAI;UAACuH,EAAE,EAAE;YAAE8C,MAAM,EAAE,MAAM;YAAEtC,MAAM,EAAE;UAAoB,CAAE;UAAAT,QAAA,eACxDjE,OAAA,CAACpD,WAAW;YAACsH,EAAE,EAAE;cAAEK,CAAC,EAAE;YAAE,CAAE;YAAAN,QAAA,gBACxBjE,OAAA,CAACzD,GAAG;cAAC2H,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBACxDjE,OAAA,CAACjB,SAAS;gBAACmF,EAAE,EAAE;kBAAEW,KAAK,EAAE,SAAS;kBAAEW,EAAE,EAAE,CAAC;kBAAEyB,QAAQ,EAAE;gBAAG;cAAE;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5DjF,OAAA,CAACxD,UAAU;gBAACmI,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAU,CAAE;gBAAAZ,QAAA,EAAC;cAEpE;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNjF,OAAA,CAACtD,IAAI;cAAC+I,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAzB,QAAA,gBACzBjE,OAAA,CAACtD,IAAI;gBAACiJ,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAA3B,QAAA,eACfjE,OAAA,CAACzD,GAAG;kBAAC2H,EAAE,EAAE;oBAAEgD,SAAS,EAAE,QAAQ;oBAAE3C,CAAC,EAAE,CAAC;oBAAEC,OAAO,EAAE,SAAS;oBAAEC,YAAY,EAAE;kBAAE,CAAE;kBAAAR,QAAA,gBAC1EjE,OAAA,CAACxD,UAAU;oBAACmI,OAAO,EAAC,IAAI;oBAACT,EAAE,EAAE;sBAAEU,UAAU,EAAE,GAAG;sBAAEC,KAAK,EAAE,SAAS;sBAAEP,EAAE,EAAE;oBAAE,CAAE;oBAAAL,QAAA,EACvED,IAAI,CAACmD;kBAAW;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACbjF,OAAA,CAACxD,UAAU;oBAACmI,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEW,KAAK,EAAE;oBAAO,CAAE;oBAAAZ,QAAA,EAAC;kBAEnD;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACPjF,OAAA,CAACtD,IAAI;gBAACiJ,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAA3B,QAAA,eACfjE,OAAA,CAACzD,GAAG;kBAAC2H,EAAE,EAAE;oBAAEgD,SAAS,EAAE,QAAQ;oBAAE3C,CAAC,EAAE,CAAC;oBAAEC,OAAO,EAAE,SAAS;oBAAEC,YAAY,EAAE;kBAAE,CAAE;kBAAAR,QAAA,gBAC1EjE,OAAA,CAACxD,UAAU;oBAACmI,OAAO,EAAC,IAAI;oBAACT,EAAE,EAAE;sBAAEU,UAAU,EAAE,GAAG;sBAAEC,KAAK,EAAE,SAAS;sBAAEP,EAAE,EAAE;oBAAE,CAAE;oBAAAL,QAAA,EACvED,IAAI,CAACoD;kBAAW;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACbjF,OAAA,CAACxD,UAAU;oBAACmI,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEW,KAAK,EAAE;oBAAO,CAAE;oBAAAZ,QAAA,GAAC,eACpC,EAACD,IAAI,CAACqD,gBAAgB,EAAC,IACtC;kBAAA;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACPjF,OAAA,CAACzD,GAAG;cAAC2H,EAAE,EAAE;gBAAEoD,EAAE,EAAE;cAAE,CAAE;cAAArD,QAAA,gBACjBjE,OAAA,CAACzD,GAAG;gBAAC2H,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,gBACnEjE,OAAA,CAACxD,UAAU;kBAACmI,OAAO,EAAC,OAAO;kBAAAV,QAAA,EAAC;gBAAS;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClDjF,OAAA,CAACxD,UAAU;kBAACmI,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE;kBAAI,CAAE;kBAAAX,QAAA,GACjDD,IAAI,CAACqD,gBAAgB,EAAC,GACzB;gBAAA;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNjF,OAAA,CAACzD,GAAG;gBAAC2H,EAAE,EAAE;kBACPqD,KAAK,EAAE,MAAM;kBACbP,MAAM,EAAE,CAAC;kBACTxC,OAAO,EAAE,SAAS;kBAClBC,YAAY,EAAE,CAAC;kBACf+C,QAAQ,EAAE;gBACZ,CAAE;gBAAAvD,QAAA,eACAjE,OAAA,CAACzD,GAAG;kBAAC2H,EAAE,EAAE;oBACPqD,KAAK,EAAE,GAAGvD,IAAI,CAACqD,gBAAgB,GAAG;oBAClCL,MAAM,EAAE,MAAM;oBACdxC,OAAO,EAAE,SAAS;oBAClBiD,UAAU,EAAE;kBACd;gBAAE;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPjF,OAAA,CAACtD,IAAI;QAACiJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvBjE,OAAA,CAACrD,IAAI;UAACuH,EAAE,EAAE;YAAE8C,MAAM,EAAE,MAAM;YAAEtC,MAAM,EAAE;UAAoB,CAAE;UAAAT,QAAA,eACxDjE,OAAA,CAACpD,WAAW;YAACsH,EAAE,EAAE;cAAEK,CAAC,EAAE;YAAE,CAAE;YAAAN,QAAA,gBACxBjE,OAAA,CAACzD,GAAG;cAAC2H,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBACxDjE,OAAA,CAAC7B,YAAY;gBAAC+F,EAAE,EAAE;kBAAEW,KAAK,EAAE,SAAS;kBAAEW,EAAE,EAAE,CAAC;kBAAEyB,QAAQ,EAAE;gBAAG;cAAE;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/DjF,OAAA,CAACxD,UAAU;gBAACmI,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAU,CAAE;gBAAAZ,QAAA,EAAC;cAEpE;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNjF,OAAA,CAACzD,GAAG;cAAC2H,EAAE,EAAE;gBAAEgD,SAAS,EAAE,QAAQ;gBAAE5C,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBACtCjE,OAAA,CAACxD,UAAU;gBAACmI,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE,SAAS;kBAAEP,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,GACvED,IAAI,CAAC4C,iBAAiB,IAAI,CAAC,EAAC,GAC/B;cAAA;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjF,OAAA,CAACxD,UAAU;gBAACmI,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE,MAAM;kBAAEP,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,EAAC;cAE1D;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZjB,IAAI,CAAC+C,2BAA2B,iBAC/B/G,OAAA,CAACxD,UAAU;gBAACmI,OAAO,EAAC,SAAS;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE,MAAM;kBAAEoC,QAAQ,EAAE;gBAAU,CAAE;gBAAAhD,QAAA,GAAC,YAC9D,EAACD,IAAI,CAAC+C,2BAA2B,EAAC,6BAC9C;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EACLjB,IAAI,CAAC6C,cAAc,gBAClB7G,OAAA,CAACzD,GAAG;cAAC2H,EAAE,EAAE;gBAAEgD,SAAS,EAAE,QAAQ;gBAAE3C,CAAC,EAAE,CAAC;gBAAEC,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAR,QAAA,gBAC1EjE,OAAA,CAACxD,UAAU;gBAACmI,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE,SAAS;kBAAEP,EAAE,EAAE;gBAAI,CAAE;gBAAAL,QAAA,EACzED,IAAI,CAAC0D;cAAkB;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACbjF,OAAA,CAACxD,UAAU;gBAACmI,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE;gBAAU,CAAE;gBAAAZ,QAAA,GAAC,4BAC1B,EAACD,IAAI,CAAC6C,cAAc,EAAC,SACjD;cAAA;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,gBAENjF,OAAA,CAACzD,GAAG;cAAC2H,EAAE,EAAE;gBAAEgD,SAAS,EAAE,QAAQ;gBAAE3C,CAAC,EAAE,CAAC;gBAAEC,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAR,QAAA,eAC1EjE,OAAA,CAACxD,UAAU;gBAACmI,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,EAC/CD,IAAI,CAAC4C,iBAAiB,GAAG,CAAC,GAAG,wBAAwB,GAAG;cAAuD;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGNjB,IAAI,CAAC2D,YAAY,IAAI3D,IAAI,CAAC2D,YAAY,CAACC,MAAM,GAAG,CAAC,iBAChD5H,OAAA,CAACrD,IAAI;MAACuH,EAAE,EAAE;QAAEQ,MAAM,EAAE;MAAoB,CAAE;MAAAT,QAAA,eACxCjE,OAAA,CAACpD,WAAW;QAACsH,EAAE,EAAE;UAAEK,CAAC,EAAE;QAAE,CAAE;QAAAN,QAAA,gBACxBjE,OAAA,CAACzD,GAAG;UAAC2H,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAL,QAAA,gBACxDjE,OAAA,CAACnB,aAAa;YAACqF,EAAE,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEW,EAAE,EAAE,CAAC;cAAEyB,QAAQ,EAAE;YAAG;UAAE;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChEjF,OAAA,CAACxD,UAAU;YAACmI,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAU,CAAE;YAAAZ,QAAA,EAAC;UAEpE;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNjF,OAAA,CAACtD,IAAI;UAAC+I,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAzB,QAAA,EACxBD,IAAI,CAAC2D,YAAY,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC7ChI,OAAA,CAACtD,IAAI;YAACiJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eAC9BjE,OAAA,CAACzD,GAAG;cAAC2H,EAAE,EAAE;gBACPK,CAAC,EAAE,CAAC;gBACJG,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,CAAC;gBACfD,OAAO,EAAEwD,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;gBAC5CP,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE;kBACTjD,OAAO,EAAE,SAAS;kBAClByD,SAAS,EAAE,kBAAkB;kBAC7BC,SAAS,EAAE;gBACb;cACF,CAAE;cAAAjE,QAAA,gBACAjE,OAAA,CAACxD,UAAU;gBAACmI,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE,MAAM;kBAAEP,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,EACtD8D,IAAI,CAAC/D;cAAI;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACbjF,OAAA,CAACxD,UAAU;gBAACmI,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAU,CAAE;gBAAAZ,QAAA,GAChE8D,IAAI,CAACI,KAAK,EAAC,GACd;cAAA;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZ+C,KAAK,KAAK,CAAC,iBACVhI,OAAA,CAAClD,IAAI;gBACHyI,KAAK,EAAC,gBAAa;gBACnBc,IAAI,EAAC,OAAO;gBACZnC,EAAE,EAAE;kBACFoD,EAAE,EAAE,CAAC;kBACL9C,OAAO,EAAE,SAAS;kBAClBK,KAAK,EAAE,OAAO;kBACdoC,QAAQ,EAAE;gBACZ;cAAE;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC,GA/B8B+C,KAAK;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgCrC,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGNjB,IAAI,CAAC2D,YAAY,CAACC,MAAM,GAAG,CAAC,iBAC3B5H,OAAA,CAACzD,GAAG;UAAC2H,EAAE,EAAE;YAAEoD,EAAE,EAAE,CAAC;YAAEJ,SAAS,EAAE;UAAS,CAAE;UAAAjD,QAAA,eACtCjE,OAAA,CAACrC,SAAS;YAAAsG,QAAA,gBACRjE,OAAA,CAACpC,gBAAgB;cAACwK,UAAU,eAAEpI,OAAA,CAACb,cAAc;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAhB,QAAA,eAC/CjE,OAAA,CAACxD,UAAU;gBAACmI,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE;gBAAU,CAAE;gBAAAZ,QAAA,GAAC,iBACrC,EAACD,IAAI,CAAC2D,YAAY,CAACC,MAAM,EAAC,SAC3C;cAAA;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACnBjF,OAAA,CAACnC,gBAAgB;cAAAoG,QAAA,eACfjE,OAAA,CAACP,eAAe;gBACduE,IAAI,EAAEA,IAAI,CAAC2D,YAAY,CAACG,GAAG,CAACC,IAAI,KAAK;kBACnC/D,IAAI,EAAE+D,IAAI,CAAC/D,IAAI;kBACfmE,KAAK,EAAE,GAAGJ,IAAI,CAACI,KAAK;gBACtB,CAAC,CAAC,CAAE;gBACJE,OAAO,EAAE,CACP;kBAAEC,KAAK,EAAE,MAAM;kBAAEC,UAAU,EAAE,MAAM;kBAAEhB,KAAK,EAAE;gBAAI,CAAC,EACjD;kBAAEe,KAAK,EAAE,OAAO;kBAAEC,UAAU,EAAE,cAAc;kBAAEhB,KAAK,EAAE,GAAG;kBAAEiB,KAAK,EAAE;gBAAQ,CAAC,CAC1E;gBACFC,UAAU,EAAE,IAAK;gBACjBC,QAAQ,EAAE;cAAG;gBAAA5D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACc,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAM0D,eAAe,GAAI3E,IAAI,iBAC3BhE,OAAA,CAACzD,GAAG;IAAA0H,QAAA,gBAEFjE,OAAA,CAACzD,GAAG;MAAC2H,EAAE,EAAE;QACPC,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,EAAE,EAAE,CAAC;QACLC,CAAC,EAAE,CAAC;QACJC,OAAO,EAAE,SAAS;QAClBC,YAAY,EAAE,CAAC;QACfC,MAAM,EAAE;MACV,CAAE;MAAAT,QAAA,gBACAjE,OAAA,CAAC3B,QAAQ;QAAC6F,EAAE,EAAE;UAAEW,KAAK,EAAE,SAAS;UAAEW,EAAE,EAAE,CAAC;UAAEyB,QAAQ,EAAE;QAAG;MAAE;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DjF,OAAA,CAACxD,UAAU;QAACmI,OAAO,EAAC,IAAI;QAACT,EAAE,EAAE;UAAEU,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAU,CAAE;QAAAZ,QAAA,EAAC;MAEpE;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGL/C,UAAU,iBACTlC,OAAA,CAACzD,GAAG;MAAC2H,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,eACjBjE,OAAA,CAACH,QAAQ;QAACmE,IAAI,EAAEA;MAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CACN,eAGDjF,OAAA,CAACrD,IAAI;MAACuH,EAAE,EAAE;QAAEI,EAAE,EAAE,CAAC;QAAEI,MAAM,EAAE;MAAoB,CAAE;MAAAT,QAAA,eAC/CjE,OAAA,CAACpD,WAAW;QAACsH,EAAE,EAAE;UAAEK,CAAC,EAAE;QAAE,CAAE;QAAAN,QAAA,gBACxBjE,OAAA,CAACzD,GAAG;UAAC2H,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAL,QAAA,gBACxDjE,OAAA,CAACjB,SAAS;YAACmF,EAAE,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEW,EAAE,EAAE,CAAC;cAAEyB,QAAQ,EAAE;YAAG;UAAE;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DjF,OAAA,CAACxD,UAAU;YAACmI,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAU,CAAE;YAAAZ,QAAA,EAAC;UAEpE;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNjF,OAAA,CAACP,eAAe;UACduE,IAAI,EAAEA,IAAI,CAAC4E,aAAa,IAAI,EAAG;UAC/BP,OAAO,EAAE,CACP;YAAEC,KAAK,EAAE,WAAW;YAAEC,UAAU,EAAE,WAAW;YAAEhB,KAAK,EAAE;UAAI,CAAC,EAC3D;YAAEe,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE,SAAS;YAAEhB,KAAK,EAAE;UAAI,CAAC,EACvD;YAAEe,KAAK,EAAE,UAAU;YAAEC,UAAU,EAAE,MAAM;YAAEhB,KAAK,EAAE,EAAE;YAAEiB,KAAK,EAAE,OAAO;YAAEK,QAAQ,EAAE;UAAS,CAAC,EACxF;YAAEP,KAAK,EAAE,eAAe;YAAEC,UAAU,EAAE,eAAe;YAAEhB,KAAK,EAAE,GAAG;YAAEiB,KAAK,EAAE,OAAO;YAAEK,QAAQ,EAAE,QAAQ;YACnGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACC,aAAa;UAAI,CAAC,EAChD;YAAEV,KAAK,EAAE,aAAa;YAAEC,UAAU,EAAE,aAAa;YAAEhB,KAAK,EAAE,GAAG;YAAEiB,KAAK,EAAE,OAAO;YAAEK,QAAQ,EAAE,QAAQ;YAC/FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACE,WAAW;UAAI,CAAC,EAC9C;YAAEX,KAAK,EAAE,iBAAiB;YAAEC,UAAU,EAAE,WAAW;YAAEhB,KAAK,EAAE,GAAG;YAAEiB,KAAK,EAAE,OAAO;YAAEK,QAAQ,EAAE,QAAQ;YACjGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACrC,eAAe;UAAI,CAAC,CAClD;UACFgC,QAAQ,EAAE;QAAG;UAAA5D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjF,OAAA,CAACrD,IAAI;MAACuH,EAAE,EAAE;QAAEQ,MAAM,EAAE;MAAoB,CAAE;MAAAT,QAAA,eACxCjE,OAAA,CAACpD,WAAW;QAACsH,EAAE,EAAE;UAAEK,CAAC,EAAE;QAAE,CAAE;QAAAN,QAAA,gBACxBjE,OAAA,CAACzD,GAAG;UAAC2H,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAL,QAAA,gBACxDjE,OAAA,CAACf,aAAa;YAACiF,EAAE,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEW,EAAE,EAAE,CAAC;cAAEyB,QAAQ,EAAE;YAAG;UAAE;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChEjF,OAAA,CAACxD,UAAU;YAACmI,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAU,CAAE;YAAAZ,QAAA,EAAC;UAEpE;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNjF,OAAA,CAACP,eAAe;UACduE,IAAI,EAAEA,IAAI,CAACkF,eAAe,IAAI,EAAG;UACjCb,OAAO,EAAE,CACP;YAAEC,KAAK,EAAE,WAAW;YAAEC,UAAU,EAAE,WAAW;YAAEhB,KAAK,EAAE;UAAI,CAAC,EAC3D;YAAEe,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE,SAAS;YAAEhB,KAAK,EAAE;UAAI,CAAC,EACvD;YAAEe,KAAK,EAAE,YAAY;YAAEC,UAAU,EAAE,QAAQ;YAAEhB,KAAK,EAAE,GAAG;YAAEiB,KAAK,EAAE,OAAO;YAAEK,QAAQ,EAAE;UAAS,CAAC,EAC7F;YAAEP,KAAK,EAAE,mBAAmB;YAAEC,UAAU,EAAE,mBAAmB;YAAEhB,KAAK,EAAE,GAAG;YAAEiB,KAAK,EAAE,OAAO;YAAEK,QAAQ,EAAE,QAAQ;YAC3GC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACI,iBAAiB;UAAI,CAAC,CACpD;UACFT,QAAQ,EAAE;QAAG;UAAA5D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CACN;EAMD,MAAMmE,uBAAuB,GAAIpF,IAAI,iBACnChE,OAAA,CAACzD,GAAG;IAAA0H,QAAA,gBAEFjE,OAAA,CAACzD,GAAG;MAAC2H,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACzFjE,OAAA,CAACxD,UAAU;QAACmI,OAAO,EAAC,IAAI;QAACT,EAAE,EAAE;UAAEU,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAe,CAAE;QAAAZ,QAAA,EAAC;MAEzE;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjF,OAAA,CAACjC,gBAAgB;QACfmH,OAAO,eACLlF,OAAA,CAAClC,MAAM;UACLqH,OAAO,EAAEjD,UAAW;UACpBkD,QAAQ,EAAGC,CAAC,IAAKlD,aAAa,CAACkD,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDN,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDM,KAAK,eACHvF,OAAA,CAACzD,GAAG;UAAC2H,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAJ,QAAA,gBACjDjE,OAAA,CAACX,aAAa;YAAC6E,EAAE,EAAE;cAAEsB,EAAE,EAAE;YAAE;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNjF,OAAA,CAACtD,IAAI;MAAC+I,SAAS;MAACC,OAAO,EAAE,CAAE;MAACxB,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACxCjE,OAAA,CAACtD,IAAI;QAACiJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvBjE,OAAA,CAACvD,KAAK;UAACyH,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAE2C,SAAS,EAAE,QAAQ;YAAE1C,OAAO,EAAE,cAAc;YAAEK,KAAK,EAAE;UAAQ,CAAE;UAAAZ,QAAA,gBAChFjE,OAAA,CAACxD,UAAU;YAACmI,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,MAAM;cAAEN,EAAE,EAAE;YAAE,CAAE;YAAAL,QAAA,GACxDD,IAAI,CAACqF,oBAAoB,EAAC,GAC7B;UAAA;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjF,OAAA,CAACxD,UAAU;YAACmI,OAAO,EAAC,OAAO;YAAAV,QAAA,EAAC;UAAY;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACrDjF,OAAA,CAACxD,UAAU;YAACmI,OAAO,EAAC,SAAS;YAAAV,QAAA,GAAED,IAAI,CAACzC,WAAW,EAAC,KAAG,EAACyC,IAAI,CAACxC,SAAS;UAAA;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPjF,OAAA,CAACtD,IAAI;QAACiJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvBjE,OAAA,CAACvD,KAAK;UAACyH,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAE2C,SAAS,EAAE,QAAQ;YAAE1C,OAAO,EAAE,WAAW;YAAEK,KAAK,EAAE;UAAQ,CAAE;UAAAZ,QAAA,gBAC7EjE,OAAA,CAACxD,UAAU;YAACmI,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,MAAM;cAAEN,EAAE,EAAE;YAAE,CAAE;YAAAL,QAAA,EACxDD,IAAI,CAACsF;UAAa;YAAAxE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACbjF,OAAA,CAACxD,UAAU;YAACmI,OAAO,EAAC,OAAO;YAAAV,QAAA,EAAC;UAAa;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPjF,OAAA,CAACtD,IAAI;QAACiJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvBjE,OAAA,CAACvD,KAAK;UAACyH,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAE2C,SAAS,EAAE,QAAQ;YAAE1C,OAAO,EAAE,cAAc;YAAEK,KAAK,EAAE;UAAQ,CAAE;UAAAZ,QAAA,gBAChFjE,OAAA,CAACxD,UAAU;YAACmI,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,MAAM;cAAEN,EAAE,EAAE;YAAE,CAAE;YAAAL,QAAA,GACxDD,IAAI,CAAC4C,iBAAiB,EAAC,GAC1B;UAAA;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjF,OAAA,CAACxD,UAAU;YAACmI,OAAO,EAAC,OAAO;YAAAV,QAAA,EAAC;UAAY;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPjF,OAAA,CAACtD,IAAI;QAACiJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvBjE,OAAA,CAACvD,KAAK;UAACyH,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAE2C,SAAS,EAAE,QAAQ;YAAE1C,OAAO,EAAE,cAAc;YAAEK,KAAK,EAAE;UAAQ,CAAE;UAAAZ,QAAA,gBAChFjE,OAAA,CAACxD,UAAU;YAACmI,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,MAAM;cAAEN,EAAE,EAAE;YAAE,CAAE;YAAAL,QAAA,GACxDsF,IAAI,CAACC,KAAK,CAACxF,IAAI,CAACqF,oBAAoB,GAAGrF,IAAI,CAACsF,aAAa,GAAG,CAAC,CAAC,EAAC,GAClE;UAAA;YAAAxE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjF,OAAA,CAACxD,UAAU;YAACmI,OAAO,EAAC,OAAO;YAAAV,QAAA,EAAC;UAAe;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGN/C,UAAU,iBACTlC,OAAA,CAACzD,GAAG;MAAC2H,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,eACjBjE,OAAA,CAACF,aAAa;QAACkE,IAAI,EAAEA;MAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGDjF,OAAA,CAACvD,KAAK;MAACyH,EAAE,EAAE;QAAEK,CAAC,EAAE;MAAE,CAAE;MAAAN,QAAA,gBAClBjE,OAAA,CAACxD,UAAU;QAACmI,OAAO,EAAC,IAAI;QAACT,EAAE,EAAE;UAAEI,EAAE,EAAE,CAAC;UAAEM,UAAU,EAAE;QAAI,CAAE;QAAAX,QAAA,EAAC;MAEzD;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjF,OAAA,CAACP,eAAe;QACduE,IAAI,EAAEA,IAAI,CAACyF,gBAAgB,IAAI,EAAG;QAClCpB,OAAO,EAAE,CACP;UAAEC,KAAK,EAAE,MAAM;UAAEC,UAAU,EAAE,MAAM;UAAEhB,KAAK,EAAE;QAAI,CAAC,EACjD;UAAEe,KAAK,EAAE,OAAO;UAAEC,UAAU,EAAE,cAAc;UAAEhB,KAAK,EAAE,GAAG;UAAEiB,KAAK,EAAE,OAAO;UAAEK,QAAQ,EAAE,QAAQ;UAC1FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACZ,KAAK;QAAI,CAAC,CACxC;QACFO,QAAQ,EAAE;MAAG;QAAA5D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACN;EAID,MAAMyE,YAAY,GAAGA,CAAA,kBACnB1J,OAAA,CAAC9C,MAAM;IAACwG,IAAI,EAAE5C,UAAW;IAAC6I,OAAO,EAAE7F,iBAAkB;IAAC8F,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAA5F,QAAA,gBAC3EjE,OAAA,CAAC7C,WAAW;MAAA8G,QAAA,EACTrD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEmF;IAAK;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eACdjF,OAAA,CAAC5C,aAAa;MAAA6G,QAAA,GACXzD,KAAK,iBACJR,OAAA,CAACjD,KAAK;QAAC+M,QAAQ,EAAC,OAAO;QAAC5F,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE,CAAE;QAAAL,QAAA,EACnCzD;MAAK;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAEDjF,OAAA,CAACtD,IAAI;QAAC+I,SAAS;QAACC,OAAO,EAAE,CAAE;QAACxB,EAAE,EAAE;UAAEoD,EAAE,EAAE;QAAE,CAAE;QAAArD,QAAA,gBACxCjE,OAAA,CAACtD,IAAI;UAACiJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAAA3B,QAAA,eAChBjE,OAAA,CAAC1C,WAAW;YAACuM,SAAS;YAAA5F,QAAA,gBACpBjE,OAAA,CAACzC,UAAU;cAAA0G,QAAA,EAAC;YAAO;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChCjF,OAAA,CAACxC,MAAM;cACLwI,KAAK,EAAE5E,QAAQ,CAACE,OAAQ;cACxBiE,KAAK,EAAC,SAAS;cACfH,QAAQ,EAAGC,CAAC,IAAKhE,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,OAAO,EAAE+D,CAAC,CAACC,MAAM,CAACU;cAAM,CAAC,CAAE;cAAA/B,QAAA,gBAEvEjE,OAAA,CAACvC,QAAQ;gBAACuI,KAAK,EAAC,OAAO;gBAAA/B,QAAA,EAAC;cAAoB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACvDjF,OAAA,CAACvC,QAAQ;gBAACuI,KAAK,EAAC,KAAK;gBAAA/B,QAAA,EAAC;cAAY;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC7CjF,OAAA,CAACvC,QAAQ;gBAACuI,KAAK,EAAC,OAAO;gBAAA/B,QAAA,EAAC;cAAc;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAINjE,UAAU,KAAK,cAAc,iBAC5BhB,OAAA,CAAAE,SAAA;UAAA+D,QAAA,gBACEjE,OAAA,CAACtD,IAAI;YAACiJ,IAAI;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACfjE,OAAA,CAACtC,SAAS;cACRmM,SAAS;cACTE,IAAI,EAAC,MAAM;cACXxE,KAAK,EAAC,aAAa;cACnBS,KAAK,EAAE5E,QAAQ,CAACG,WAAY;cAC5B6D,QAAQ,EAAGC,CAAC,IAAKhE,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAE8D,CAAC,CAACC,MAAM,CAACU;cAAM,CAAC,CAAE;cAC3EgE,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAnF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjF,OAAA,CAACtD,IAAI;YAACiJ,IAAI;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACfjE,OAAA,CAACtC,SAAS;cACRmM,SAAS;cACTE,IAAI,EAAC,MAAM;cACXxE,KAAK,EAAC,WAAW;cACjBS,KAAK,EAAE5E,QAAQ,CAACI,SAAU;cAC1B4D,QAAQ,EAAGC,CAAC,IAAKhE,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,SAAS,EAAE6D,CAAC,CAACC,MAAM,CAACU;cAAM,CAAC,CAAE;cACzEgE,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAnF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAChBjF,OAAA,CAAC3C,aAAa;MAAA4G,QAAA,gBACZjE,OAAA,CAACnD,MAAM;QAACqN,OAAO,EAAEpG,iBAAkB;QAAAG,QAAA,EAAC;MAAO;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACpDjF,OAAA,CAACnD,MAAM;QACLqN,OAAO,EAAErG,oBAAqB;QAC9Bc,OAAO,EAAC,WAAW;QACnBwF,QAAQ,EAAE7J,OAAQ;QAClB8J,SAAS,EAAE9J,OAAO,gBAAGN,OAAA,CAAChD,gBAAgB;UAACqJ,IAAI,EAAE;QAAG;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGjF,OAAA,CAACvB,cAAc;UAAAqG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAhB,QAAA,EAExE3D,OAAO,GAAG,gBAAgB,GAAG;MAAe;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACT;EAED,oBACEjF,OAAA,CAACzD,GAAG;IAAC8N,SAAS,EAAC,sCAAsC;IAAApG,QAAA,gBAEnDjE,OAAA,CAACzD,GAAG;MAAC2H,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,UAAU;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,eACpFjE,OAAA,CAACT,eAAe;QAAAuF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EAGL3E,OAAO,iBACNN,OAAA,CAACzD,GAAG;MAAC2H,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEkG,EAAE,EAAE;MAAE,CAAE;MAAArG,QAAA,eAC5DjE,OAAA,CAAChD,gBAAgB;QAAA8H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGDjF,OAAA,CAACzD,GAAG;MAAC2H,EAAE,EAAE;QAAEoD,EAAE,EAAE;MAAE,CAAE;MAAArD,QAAA,gBAEjBjE,OAAA,CAACzD,GAAG;QAAC2H,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE,CAAE;QAAAL,QAAA,gBACjBjE,OAAA,CAACxD,UAAU;UAACmI,OAAO,EAAC,IAAI;UAACT,EAAE,EAAE;YAAEU,UAAU,EAAE,GAAG;YAAEC,KAAK,EAAE,SAAS;YAAEP,EAAE,EAAE,CAAC;YAAE4C,SAAS,EAAE;UAAS,CAAE;UAAAjD,QAAA,EAAC;QAEhG;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjF,OAAA,CAACtD,IAAI;UAAC+I,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAzB,QAAA,gBAEzBjE,OAAA,CAACtD,IAAI;YAACiJ,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eAC7BjE,OAAA,CAACrD,IAAI;cACH0N,SAAS,EAAE,eAAenJ,kBAAkB,KAAK,UAAU,GAAG,sBAAsB,GAAG,EAAE,EAAG;cAC5FgD,EAAE,EAAE;gBACF8C,MAAM,EAAE,OAAO;gBACfuD,MAAM,EAAE,SAAS;gBACjB7F,MAAM,EAAExD,kBAAkB,KAAK,UAAU,GAAG,mBAAmB,GAAG,mBAAmB;gBACrFsD,OAAO,EAAEtD,kBAAkB,KAAK,UAAU,GAAG,SAAS,GAAG,OAAO;gBAChEuG,UAAU,EAAE;cACd,CAAE;cACFyC,OAAO,EAAEA,CAAA,KAAM/I,qBAAqB,CAAC,UAAU,CAAE;cAAA8C,QAAA,eAEjDjE,OAAA,CAACpD,WAAW;gBAACsH,EAAE,EAAE;kBAAEK,CAAC,EAAE,CAAC;kBAAE2C,SAAS,EAAE,QAAQ;kBAAEF,MAAM,EAAE,MAAM;kBAAE7C,OAAO,EAAE,MAAM;kBAAEqG,aAAa,EAAE,QAAQ;kBAAEpG,cAAc,EAAE;gBAAS,CAAE;gBAAAH,QAAA,gBACjIjE,OAAA,CAAC/B,cAAc;kBAACiG,EAAE,EAAE;oBAAE+C,QAAQ,EAAE,EAAE;oBAAEpC,KAAK,EAAE,SAAS;oBAAEP,EAAE,EAAE;kBAAE;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjEjF,OAAA,CAACxD,UAAU;kBAACmI,OAAO,EAAC,WAAW;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE,GAAG;oBAAEN,EAAE,EAAE,GAAG;oBAAE2C,QAAQ,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,EAAC;gBAEtF;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbjF,OAAA,CAACxD,UAAU;kBAACmI,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEoC,QAAQ,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,EAAC;gBAEvE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPjF,OAAA,CAACtD,IAAI;YAACiJ,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eAC7BjE,OAAA,CAACrD,IAAI;cACHuH,EAAE,EAAE;gBACF8C,MAAM,EAAE,OAAO;gBACfuD,MAAM,EAAE,SAAS;gBACjB7F,MAAM,EAAExD,kBAAkB,KAAK,KAAK,GAAG,mBAAmB,GAAG,mBAAmB;gBAChFsD,OAAO,EAAEtD,kBAAkB,KAAK,KAAK,GAAG,SAAS,GAAG,OAAO;gBAC3DuG,UAAU,EAAE;cACd,CAAE;cACFyC,OAAO,EAAEA,CAAA,KAAM/I,qBAAqB,CAAC,KAAK,CAAE;cAAA8C,QAAA,eAE5CjE,OAAA,CAACpD,WAAW;gBAACsH,EAAE,EAAE;kBAAEK,CAAC,EAAE,CAAC;kBAAE2C,SAAS,EAAE,QAAQ;kBAAEF,MAAM,EAAE,MAAM;kBAAE7C,OAAO,EAAE,MAAM;kBAAEqG,aAAa,EAAE,QAAQ;kBAAEpG,cAAc,EAAE;gBAAS,CAAE;gBAAAH,QAAA,gBACjIjE,OAAA,CAAC3B,QAAQ;kBAAC6F,EAAE,EAAE;oBAAE+C,QAAQ,EAAE,EAAE;oBAAEpC,KAAK,EAAE,SAAS;oBAAEP,EAAE,EAAE;kBAAE;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3DjF,OAAA,CAACxD,UAAU;kBAACmI,OAAO,EAAC,WAAW;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE,GAAG;oBAAEN,EAAE,EAAE,GAAG;oBAAE2C,QAAQ,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,EAAC;gBAEtF;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbjF,OAAA,CAACxD,UAAU;kBAACmI,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEoC,QAAQ,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,EAAC;gBAEvE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAKPjF,OAAA,CAACtD,IAAI;YAACiJ,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eAC7BjE,OAAA,CAACrD,IAAI;cACHuH,EAAE,EAAE;gBACF8C,MAAM,EAAE,OAAO;gBACfuD,MAAM,EAAE,SAAS;gBACjB7F,MAAM,EAAExD,kBAAkB,KAAK,cAAc,GAAG,mBAAmB,GAAG,mBAAmB;gBACzFsD,OAAO,EAAEtD,kBAAkB,KAAK,cAAc,GAAG,SAAS,GAAG,OAAO;gBACpEuG,UAAU,EAAE;cACd,CAAE;cACFyC,OAAO,EAAEA,CAAA,KAAM/I,qBAAqB,CAAC,cAAc,CAAE;cAAA8C,QAAA,eAErDjE,OAAA,CAACpD,WAAW;gBAACsH,EAAE,EAAE;kBAAEK,CAAC,EAAE,CAAC;kBAAE2C,SAAS,EAAE,QAAQ;kBAAEF,MAAM,EAAE,MAAM;kBAAE7C,OAAO,EAAE,MAAM;kBAAEqG,aAAa,EAAE,QAAQ;kBAAEpG,cAAc,EAAE;gBAAS,CAAE;gBAAAH,QAAA,gBACjIjE,OAAA,CAAC7B,YAAY;kBAAC+F,EAAE,EAAE;oBAAE+C,QAAQ,EAAE,EAAE;oBAAEpC,KAAK,EAAE,SAAS;oBAAEP,EAAE,EAAE;kBAAE;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/DjF,OAAA,CAACxD,UAAU;kBAACmI,OAAO,EAAC,WAAW;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE,GAAG;oBAAEN,EAAE,EAAE,GAAG;oBAAE2C,QAAQ,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,EAAC;gBAEtF;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbjF,OAAA,CAACxD,UAAU;kBAACmI,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEoC,QAAQ,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,EAAC;gBAEvE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNjF,OAAA,CAACzD,GAAG;QAAC2H,EAAE,EAAE;UAAEuG,SAAS,EAAE;QAAQ,CAAE;QAAAxG,QAAA,GAE7B/C,kBAAkB,KAAK,UAAU,iBAChClB,OAAA,CAACvD,KAAK;UAACyH,EAAE,EAAE;YAAEK,CAAC,EAAE;UAAE,CAAE;UAAAN,QAAA,EACjBvC,WAAW,CAACE,QAAQ,gBACnB5B,OAAA,CAACzD,GAAG;YAAA0H,QAAA,gBACFjE,OAAA,CAACzD,GAAG;cAAC2H,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBAC9DjE,OAAA,CAACnD,MAAM;gBACLuN,SAAS,eAAEpK,OAAA,CAACzB,YAAY;kBAAAuG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BiF,OAAO,EAAEA,CAAA,KAAMjH,wBAAwB,CAAC,UAAU,EAAE,KAAK,CAAE;gBAC3D0B,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBACfX,EAAE,EAAE;kBAAEsB,EAAE,EAAE;gBAAE,CAAE;gBAAAvB,QAAA,EACf;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjF,OAAA,CAACnD,MAAM;gBACLuN,SAAS,eAAEpK,OAAA,CAACzB,YAAY;kBAAAuG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BiF,OAAO,EAAEA,CAAA,KAAMjH,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAE;gBAC7D0B,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBAAAZ,QAAA,EAChB;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLlB,oBAAoB,CAACrC,WAAW,CAACE,QAAQ,CAAC;UAAA;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,GACJ3E,OAAO,gBACTN,OAAA,CAACN,UAAU;YACTqK,IAAI,EAAC,SAAS;YACd7G,UAAU,EAAC,UAAU;YACrB6C,KAAK,EAAC,mCAAmC;YACzC2E,WAAW,EAAC;UAAsD;YAAA5F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,gBAEFjF,OAAA,CAACN,UAAU;YACTqK,IAAI,EAAC,OAAO;YACZ7G,UAAU,EAAC,UAAU;YACrB6C,KAAK,EAAC,wBAAwB;YAC9B2E,WAAW,EAAC,mFAAmF;YAC/FC,OAAO,EAAEA,CAAA,KAAM;cACbpK,UAAU,CAAC,IAAI,CAAC;cAChBf,aAAa,CAAC8C,iBAAiB,CAACjC,UAAU,EAAE,OAAO,CAAC,CACjDuK,IAAI,CAAC5G,IAAI,IAAI;gBACZrC,cAAc,CAAC4B,IAAI,KAAK;kBACtB,GAAGA,IAAI;kBACP3B,QAAQ,EAAEoC,IAAI,CAACtB;gBACjB,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;gBACZC,OAAO,CAACjC,KAAK,CAAC,iCAAiC,EAAEgC,GAAG,CAAC;cACvD,CAAC,CAAC,CACDqI,OAAO,CAAC,MAAM;gBACbtK,UAAU,CAAC,KAAK,CAAC;cACnB,CAAC,CAAC;YACN,CAAE;YACFD,OAAO,EAAEA;UAAQ;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAGA/D,kBAAkB,KAAK,KAAK,iBAC3BlB,OAAA,CAACvD,KAAK;UAACyH,EAAE,EAAE;YAAEK,CAAC,EAAE;UAAE,CAAE;UAAAN,QAAA,EACjBvC,WAAW,CAACG,GAAG,gBACd7B,OAAA,CAACzD,GAAG;YAAA0H,QAAA,gBACFjE,OAAA,CAACzD,GAAG;cAAC2H,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBAC9DjE,OAAA,CAACnD,MAAM;gBACLuN,SAAS,eAAEpK,OAAA,CAACzB,YAAY;kBAAAuG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BiF,OAAO,EAAEA,CAAA,KAAMjH,wBAAwB,CAAC,KAAK,EAAE,KAAK,CAAE;gBACtD0B,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBACfX,EAAE,EAAE;kBAAEsB,EAAE,EAAE;gBAAE,CAAE;gBAAAvB,QAAA,EACf;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjF,OAAA,CAACnD,MAAM;gBACLuN,SAAS,eAAEpK,OAAA,CAACzB,YAAY;kBAAAuG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BiF,OAAO,EAAEA,CAAA,KAAMjH,wBAAwB,CAAC,KAAK,EAAE,OAAO,CAAE;gBACxD0B,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBAAAZ,QAAA,EAChB;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL0D,eAAe,CAACjH,WAAW,CAACG,GAAG,CAAC;UAAA;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,GACJ3E,OAAO,gBACTN,OAAA,CAACN,UAAU;YACTqK,IAAI,EAAC,SAAS;YACd7G,UAAU,EAAC,KAAK;YAChB6C,KAAK,EAAC,mCAAmC;YACzC2E,WAAW,EAAC;UAAyC;YAAA5F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,gBAEFjF,OAAA,CAACN,UAAU;YACTqK,IAAI,EAAC,OAAO;YACZ7G,UAAU,EAAC,KAAK;YAChB6C,KAAK,EAAC,wBAAwB;YAC9B2E,WAAW,EAAC,gFAAgF;YAC5FC,OAAO,EAAEA,CAAA,KAAM;cACbpK,UAAU,CAAC,IAAI,CAAC;cAChBf,aAAa,CAACoD,mBAAmB,CAACvC,UAAU,EAAE,OAAO,CAAC,CACnDuK,IAAI,CAAC5G,IAAI,IAAI;gBACZrC,cAAc,CAAC4B,IAAI,KAAK;kBACtB,GAAGA,IAAI;kBACP1B,GAAG,EAAEmC,IAAI,CAACtB;gBACZ,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;gBACZC,OAAO,CAACjC,KAAK,CAAC,4BAA4B,EAAEgC,GAAG,CAAC;cAClD,CAAC,CAAC,CACDqI,OAAO,CAAC,MAAM;gBACbtK,UAAU,CAAC,KAAK,CAAC;cACnB,CAAC,CAAC;YACN,CAAE;YACFD,OAAO,EAAEA;UAAQ;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAKA/D,kBAAkB,KAAK,cAAc,iBACpClB,OAAA,CAACvD,KAAK;UAACyH,EAAE,EAAE;YAAEK,CAAC,EAAE;UAAE,CAAE;UAAAN,QAAA,EACjBvC,WAAW,CAACO,WAAW,gBACtBjC,OAAA,CAACzD,GAAG;YAAA0H,QAAA,gBACFjE,OAAA,CAACzD,GAAG;cAAC2H,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBAC9DjE,OAAA,CAACnD,MAAM;gBACLuN,SAAS,eAAEpK,OAAA,CAACzB,YAAY;kBAAAuG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BiF,OAAO,EAAEA,CAAA,KAAMjH,wBAAwB,CAAC,cAAc,EAAE,KAAK,CAAE;gBAC/D0B,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBACfX,EAAE,EAAE;kBAAEsB,EAAE,EAAE;gBAAE,CAAE;gBAAAvB,QAAA,EACf;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjF,OAAA,CAACnD,MAAM;gBACLuN,SAAS,eAAEpK,OAAA,CAACzB,YAAY;kBAAAuG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BiF,OAAO,EAAEA,CAAA,KAAMjH,wBAAwB,CAAC,cAAc,EAAE,OAAO,CAAE;gBACjE0B,OAAO,EAAC,UAAU;gBAClB0B,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,SAAS;gBAAAZ,QAAA,EAChB;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLmE,uBAAuB,CAAC1H,WAAW,CAACO,WAAW,CAAC;UAAA;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,gBAENjF,OAAA,CAACN,UAAU;YACTqK,IAAI,EAAC,iBAAiB;YACtB7G,UAAU,EAAC,cAAc;YACzB6C,KAAK,EAAC,sBAAsB;YAC5B2E,WAAW,EAAC,8GAA2G;YACvHI,WAAW,EAAC,mBAAmB;YAC/BC,QAAQ,EAAEA,CAAA,KAAM;cACd9J,aAAa,CAAC,cAAc,CAAC;cAC7B;cACA,MAAM+J,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;cACxB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC;cAC5BC,SAAS,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;cAExC/J,WAAW,CAAC;gBACV,GAAGD,QAAQ;gBACXG,WAAW,EAAE2J,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAClD9J,SAAS,EAAEwJ,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;cAC7C,CAAC,CAAC;cACFvK,aAAa,CAAC,IAAI,CAAC;YACrB;UAAE;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLyE,YAAY,CAAC,CAAC;EAAA;IAAA5E,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAAC7E,EAAA,CA58BID,iBAAiB;EAAA,QACEb,SAAS;AAAA;AAAAiM,EAAA,GAD5BpL,iBAAiB;AA88BvB,eAAeA,iBAAiB;AAAC,IAAAoL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}