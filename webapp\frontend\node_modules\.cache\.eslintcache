[{"C:\\CMS\\webapp\\frontend\\src\\index.js": "1", "C:\\CMS\\webapp\\frontend\\src\\App.js": "2", "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js": "3", "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js": "4", "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js": "5", "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js": "6", "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js": "7", "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js": "8", "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js": "9", "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js": "10", "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js": "11", "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js": "12", "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js": "13", "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js": "14", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js": "15", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCaviPage.js": "16", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js": "17", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js": "18", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js": "19", "C:\\CMS\\webapp\\frontend\\src\\config.js": "20", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js": "21", "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js": "22", "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx": "23", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js": "24", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js": "25", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js": "26", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js": "27", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\StoricoUtilizzoPage.js": "28", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js": "29", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\MetriPosatiSemplificatoPage.js": "30", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaBobinaPage.js": "31", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaCavoPage.js": "32", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\InserisciMetriPage.js": "33", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\CollegamentiPage.js": "34", "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js": "35", "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js": "36", "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js": "37", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js": "38", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js": "39", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js": "40", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js": "41", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js": "42", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js": "43", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js": "44", "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js": "45", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js": "46", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js": "47", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCavi.js": "48", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js": "49", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js": "50", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\PosaCaviCollegamenti.js": "51", "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js": "52", "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js": "53", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js": "54", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js": "55", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js": "56", "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js": "57", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js": "58", "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js": "59", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js": "60", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js": "61", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx": "62", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx": "63", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx": "64", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx": "65", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js": "66", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js": "67", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js": "68", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaForm.js": "69", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriForm.js": "70", "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js": "71", "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js": "72", "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js": "73", "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js": "74", "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js": "75", "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js": "76", "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js": "77", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js": "78", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js": "79", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js": "80", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js": "81", "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js": "82", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js": "83", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js": "84", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js": "85", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js": "86", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js": "87", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js": "88", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js": "89", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js": "90", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js": "91", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js": "92"}, {"size": 557, "mtime": 1746952718482, "results": "93", "hashOfConfig": "94"}, {"size": 2728, "mtime": 1746952740200, "results": "95", "hashOfConfig": "94"}, {"size": 996, "mtime": 1746970152489, "results": "96", "hashOfConfig": "94"}, {"size": 10788, "mtime": 1746864244183, "results": "97", "hashOfConfig": "94"}, {"size": 18405, "mtime": 1748034045686, "results": "98", "hashOfConfig": "94"}, {"size": 6001, "mtime": 1748200429907, "results": "99", "hashOfConfig": "94"}, {"size": 2216, "mtime": 1746640055487, "results": "100", "hashOfConfig": "94"}, {"size": 7394, "mtime": 1748034003517, "results": "101", "hashOfConfig": "94"}, {"size": 6749, "mtime": 1746282201800, "results": "102", "hashOfConfig": "94"}, {"size": 21982, "mtime": 1748068137555, "results": "103", "hashOfConfig": "94"}, {"size": 2535, "mtime": 1746647873596, "results": "104", "hashOfConfig": "94"}, {"size": 2050, "mtime": 1746647945415, "results": "105", "hashOfConfig": "94"}, {"size": 700, "mtime": 1747545501078, "results": "106", "hashOfConfig": "94"}, {"size": 17518, "mtime": 1748664526035, "results": "107", "hashOfConfig": "94"}, {"size": 3999, "mtime": 1746943038491, "results": "108", "hashOfConfig": "94"}, {"size": 4001, "mtime": 1748121689116, "results": "109", "hashOfConfig": "94"}, {"size": 1630, "mtime": 1746336079554, "results": "110", "hashOfConfig": "94"}, {"size": 3070, "mtime": 1746637986362, "results": "111", "hashOfConfig": "94"}, {"size": 37999, "mtime": 1748666433118, "results": "112", "hashOfConfig": "94"}, {"size": 324, "mtime": 1748675785146, "results": "113", "hashOfConfig": "94"}, {"size": 9068, "mtime": 1746856425683, "results": "114", "hashOfConfig": "94"}, {"size": 2210, "mtime": 1747432283057, "results": "115", "hashOfConfig": "94"}, {"size": 4494, "mtime": 1748121063631, "results": "116", "hashOfConfig": "94"}, {"size": 37751, "mtime": 1748686911338, "results": "117", "hashOfConfig": "94"}, {"size": 3969, "mtime": 1746864496870, "results": "118", "hashOfConfig": "94"}, {"size": 3609, "mtime": 1746944025177, "results": "119", "hashOfConfig": "94"}, {"size": 4142, "mtime": 1746942978805, "results": "120", "hashOfConfig": "94"}, {"size": 3986, "mtime": 1746864510624, "results": "121", "hashOfConfig": "94"}, {"size": 3973, "mtime": 1746864489032, "results": "122", "hashOfConfig": "94"}, {"size": 2975, "mtime": 1747554796402, "results": "123", "hashOfConfig": "94"}, {"size": 3429, "mtime": 1747721794176, "results": "124", "hashOfConfig": "94"}, {"size": 3109, "mtime": 1747824114392, "results": "125", "hashOfConfig": "94"}, {"size": 2929, "mtime": 1747655572696, "results": "126", "hashOfConfig": "94"}, {"size": 3302, "mtime": 1748000902435, "results": "127", "hashOfConfig": "94"}, {"size": 5597, "mtime": 1748070089791, "results": "128", "hashOfConfig": "94"}, {"size": 5880, "mtime": 1748121404574, "results": "129", "hashOfConfig": "94"}, {"size": 3889, "mtime": 1748664890350, "results": "130", "hashOfConfig": "94"}, {"size": 4720, "mtime": 1746771178920, "results": "131", "hashOfConfig": "94"}, {"size": 7121, "mtime": 1746281148395, "results": "132", "hashOfConfig": "94"}, {"size": 7958, "mtime": 1746280443400, "results": "133", "hashOfConfig": "94"}, {"size": 6259, "mtime": 1746965906057, "results": "134", "hashOfConfig": "94"}, {"size": 4215, "mtime": 1746278746358, "results": "135", "hashOfConfig": "94"}, {"size": 1273, "mtime": 1746809069006, "results": "136", "hashOfConfig": "94"}, {"size": 14270, "mtime": 1748371983481, "results": "137", "hashOfConfig": "94"}, {"size": 2752, "mtime": 1747022186740, "results": "138", "hashOfConfig": "94"}, {"size": 1072, "mtime": 1746637929350, "results": "139", "hashOfConfig": "94"}, {"size": 6745, "mtime": 1747545492454, "results": "140", "hashOfConfig": "94"}, {"size": 38569, "mtime": 1748371531457, "results": "141", "hashOfConfig": "94"}, {"size": 23333, "mtime": 1746463652843, "results": "142", "hashOfConfig": "94"}, {"size": 47271, "mtime": 1748072224692, "results": "143", "hashOfConfig": "94"}, {"size": 38669, "mtime": 1748199713253, "results": "144", "hashOfConfig": "94"}, {"size": 1947, "mtime": 1748120984640, "results": "145", "hashOfConfig": "94"}, {"size": 54895, "mtime": 1748370360136, "results": "146", "hashOfConfig": "94"}, {"size": 14635, "mtime": 1748666301849, "results": "147", "hashOfConfig": "94"}, {"size": 8538, "mtime": 1748666450738, "results": "148", "hashOfConfig": "94"}, {"size": 11771, "mtime": 1746948731812, "results": "149", "hashOfConfig": "94"}, {"size": 2211, "mtime": 1748686293878, "results": "150", "hashOfConfig": "94"}, {"size": 9215, "mtime": 1748668814050, "results": "151", "hashOfConfig": "94"}, {"size": 10993, "mtime": 1747154871546, "results": "152", "hashOfConfig": "94"}, {"size": 12150, "mtime": 1748205557322, "results": "153", "hashOfConfig": "94"}, {"size": 23685, "mtime": 1748676563925, "results": "154", "hashOfConfig": "94"}, {"size": 7032, "mtime": 1748069273238, "results": "155", "hashOfConfig": "94"}, {"size": 8589, "mtime": 1748207111023, "results": "156", "hashOfConfig": "94"}, {"size": 9979, "mtime": 1748069243848, "results": "157", "hashOfConfig": "94"}, {"size": 10821, "mtime": 1748069202177, "results": "158", "hashOfConfig": "94"}, {"size": 36555, "mtime": 1747684003188, "results": "159", "hashOfConfig": "94"}, {"size": 9483, "mtime": 1747194869458, "results": "160", "hashOfConfig": "94"}, {"size": 13900, "mtime": 1748182219170, "results": "161", "hashOfConfig": "94"}, {"size": 48588, "mtime": 1747948123233, "results": "162", "hashOfConfig": "94"}, {"size": 92270, "mtime": 1748123070273, "results": "163", "hashOfConfig": "94"}, {"size": 522, "mtime": 1747022186711, "results": "164", "hashOfConfig": "94"}, {"size": 6612, "mtime": 1748069456201, "results": "165", "hashOfConfig": "94"}, {"size": 3796, "mtime": 1747022186720, "results": "166", "hashOfConfig": "94"}, {"size": 1703, "mtime": 1746972529152, "results": "167", "hashOfConfig": "94"}, {"size": 19892, "mtime": 1747554544219, "results": "168", "hashOfConfig": "94"}, {"size": 12050, "mtime": 1747547543421, "results": "169", "hashOfConfig": "94"}, {"size": 1686, "mtime": 1746946499500, "results": "170", "hashOfConfig": "94"}, {"size": 5145, "mtime": 1746914029633, "results": "171", "hashOfConfig": "94"}, {"size": 9788, "mtime": 1747491601484, "results": "172", "hashOfConfig": "94"}, {"size": 22179, "mtime": 1747432554979, "results": "173", "hashOfConfig": "94"}, {"size": 2258, "mtime": 1746946368534, "results": "174", "hashOfConfig": "94"}, {"size": 4094, "mtime": 1748161663641, "results": "175", "hashOfConfig": "94"}, {"size": 5273, "mtime": 1747946737459, "results": "176", "hashOfConfig": "94"}, {"size": 4346, "mtime": 1747491472989, "results": "177", "hashOfConfig": "94"}, {"size": 15571, "mtime": 1747980774491, "results": "178", "hashOfConfig": "94"}, {"size": 7839, "mtime": 1748664451344, "results": "179", "hashOfConfig": "94"}, {"size": 6529, "mtime": 1748664406267, "results": "180", "hashOfConfig": "94"}, {"size": 15739, "mtime": 1748664968476, "results": "181", "hashOfConfig": "94"}, {"size": 6448, "mtime": 1748664917658, "results": "182", "hashOfConfig": "94"}, {"size": 5536, "mtime": 1748670096009, "results": "183", "hashOfConfig": "94"}, {"size": 5457, "mtime": 1748666884369, "results": "184", "hashOfConfig": "94"}, {"size": 5605, "mtime": 1748666925194, "results": "185", "hashOfConfig": "94"}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f0jzw9", {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\CMS\\webapp\\frontend\\src\\index.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\App.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js", ["462", "463", "464", "465"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js", ["466", "467", "468", "469", "470", "471", "472", "473", "474", "475"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js", ["476"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js", ["477", "478", "479", "480", "481", "482", "483", "484", "485", "486"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js", ["487", "488", "489", "490", "491", "492", "493", "494", "495", "496", "497", "498", "499", "500", "501"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCaviPage.js", ["502", "503", "504", "505"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js", ["506"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js", ["507", "508"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js", ["509", "510", "511", "512", "513", "514", "515", "516", "517", "518", "519", "520", "521", "522", "523"], [], "C:\\CMS\\webapp\\frontend\\src\\config.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js", ["524"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx", ["525"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js", ["526", "527"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js", ["528", "529"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\StoricoUtilizzoPage.js", ["530", "531"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js", ["532", "533"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\MetriPosatiSemplificatoPage.js", ["534"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaBobinaPage.js", ["535", "536", "537", "538", "539", "540", "541"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaCavoPage.js", ["542", "543", "544"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\InserisciMetriPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\CollegamentiPage.js", ["545", "546", "547", "548", "549", "550", "551"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js", ["552", "553"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js", ["554", "555"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js", ["556", "557"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js", ["558", "559"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCavi.js", ["560", "561", "562", "563", "564", "565", "566", "567"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js", ["568", "569", "570", "571", "572", "573", "574", "575", "576", "577", "578", "579"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js", ["580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590", "591", "592", "593"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\PosaCaviCollegamenti.js", ["594", "595", "596", "597", "598", "599", "600"], ["601"], "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js", ["602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js", ["625", "626"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js", ["627", "628"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js", ["629", "630", "631", "632", "633", "634", "635", "636", "637", "638", "639"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js", ["640"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js", ["641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661", "662"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx", ["663"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx", ["664"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx", ["665"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js", ["666", "667", "668", "669", "670", "671", "672", "673", "674", "675", "676"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js", ["677"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js", ["678", "679", "680", "681"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaForm.js", ["682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriForm.js", ["706", "707", "708", "709", "710", "711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js", ["722", "723"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js", ["724", "725"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js", ["726", "727"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js", ["728", "729", "730", "731", "732", "733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743", "744", "745", "746", "747"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js", ["748", "749", "750", "751", "752", "753", "754", "755"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js", ["756"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js", ["757", "758", "759"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js", ["760"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js", ["761"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js", ["762"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js", ["763"], [], {"ruleId": "764", "severity": 1, "message": "765", "line": 78, "column": 11, "nodeType": "766", "messageId": "767", "endLine": 78, "endColumn": 115}, {"ruleId": "764", "severity": 1, "message": "765", "line": 80, "column": 11, "nodeType": "766", "messageId": "767", "endLine": 80, "endColumn": 107}, {"ruleId": "764", "severity": 1, "message": "765", "line": 86, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 86, "endColumn": 105}, {"ruleId": "764", "severity": 1, "message": "765", "line": 89, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 89, "endColumn": 41}, {"ruleId": "768", "severity": 1, "message": "769", "line": 13, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 13, "endColumn": 9}, {"ruleId": "768", "severity": 1, "message": "772", "line": 20, "column": 25, "nodeType": "770", "messageId": "771", "endLine": 20, "endColumn": 34}, {"ruleId": "768", "severity": 1, "message": "773", "line": 21, "column": 19, "nodeType": "770", "messageId": "771", "endLine": 21, "endColumn": 35}, {"ruleId": "768", "severity": 1, "message": "774", "line": 22, "column": 12, "nodeType": "770", "messageId": "771", "endLine": 22, "endColumn": 21}, {"ruleId": "768", "severity": 1, "message": "775", "line": 23, "column": 18, "nodeType": "770", "messageId": "771", "endLine": 23, "endColumn": 28}, {"ruleId": "768", "severity": 1, "message": "776", "line": 56, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 56, "endColumn": 22}, {"ruleId": "768", "severity": 1, "message": "777", "line": 57, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 57, "endColumn": 23}, {"ruleId": "768", "severity": 1, "message": "778", "line": 58, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 58, "endColumn": 26}, {"ruleId": "768", "severity": 1, "message": "779", "line": 59, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 59, "endColumn": 22}, {"ruleId": "768", "severity": 1, "message": "780", "line": 68, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 68, "endColumn": 29}, {"ruleId": "768", "severity": 1, "message": "781", "line": 1, "column": 8, "nodeType": "770", "messageId": "771", "endLine": 1, "endColumn": 13}, {"ruleId": "768", "severity": 1, "message": "782", "line": 2, "column": 27, "nodeType": "770", "messageId": "771", "endLine": 2, "endColumn": 31}, {"ruleId": "768", "severity": 1, "message": "783", "line": 2, "column": 33, "nodeType": "770", "messageId": "771", "endLine": 2, "endColumn": 37}, {"ruleId": "768", "severity": 1, "message": "784", "line": 2, "column": 39, "nodeType": "770", "messageId": "771", "endLine": 2, "endColumn": 50}, {"ruleId": "768", "severity": 1, "message": "785", "line": 2, "column": 52, "nodeType": "770", "messageId": "771", "endLine": 2, "endColumn": 66}, {"ruleId": "768", "severity": 1, "message": "769", "line": 2, "column": 68, "nodeType": "770", "messageId": "771", "endLine": 2, "endColumn": 74}, {"ruleId": "768", "severity": 1, "message": "772", "line": 5, "column": 25, "nodeType": "770", "messageId": "771", "endLine": 5, "endColumn": 34}, {"ruleId": "768", "severity": 1, "message": "773", "line": 6, "column": 19, "nodeType": "770", "messageId": "771", "endLine": 6, "endColumn": 35}, {"ruleId": "768", "severity": 1, "message": "774", "line": 7, "column": 12, "nodeType": "770", "messageId": "771", "endLine": 7, "endColumn": 21}, {"ruleId": "768", "severity": 1, "message": "775", "line": 8, "column": 18, "nodeType": "770", "messageId": "771", "endLine": 8, "endColumn": 28}, {"ruleId": "768", "severity": 1, "message": "786", "line": 43, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 43, "endColumn": 19}, {"ruleId": "768", "severity": 1, "message": "783", "line": 9, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 9, "endColumn": 7}, {"ruleId": "768", "severity": 1, "message": "784", "line": 10, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 10, "endColumn": 14}, {"ruleId": "768", "severity": 1, "message": "787", "line": 11, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 11, "endColumn": 14}, {"ruleId": "768", "severity": 1, "message": "782", "line": 12, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 12, "endColumn": 7}, {"ruleId": "768", "severity": 1, "message": "788", "line": 13, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 13, "endColumn": 10}, {"ruleId": "768", "severity": 1, "message": "789", "line": 18, "column": 11, "nodeType": "770", "messageId": "771", "endLine": 18, "endColumn": 19}, {"ruleId": "768", "severity": 1, "message": "790", "line": 19, "column": 15, "nodeType": "770", "messageId": "771", "endLine": 19, "endColumn": 27}, {"ruleId": "768", "severity": 1, "message": "791", "line": 20, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 20, "endColumn": 17}, {"ruleId": "768", "severity": 1, "message": "792", "line": 21, "column": 11, "nodeType": "770", "messageId": "771", "endLine": 21, "endColumn": 19}, {"ruleId": "768", "severity": 1, "message": "793", "line": 22, "column": 13, "nodeType": "770", "messageId": "771", "endLine": 22, "endColumn": 23}, {"ruleId": "768", "severity": 1, "message": "794", "line": 23, "column": 14, "nodeType": "770", "messageId": "771", "endLine": 23, "endColumn": 25}, {"ruleId": "768", "severity": 1, "message": "795", "line": 28, "column": 8, "nodeType": "770", "messageId": "771", "endLine": 28, "endColumn": 17}, {"ruleId": "768", "severity": 1, "message": "796", "line": 31, "column": 11, "nodeType": "770", "messageId": "771", "endLine": 31, "endColumn": 26}, {"ruleId": "768", "severity": 1, "message": "797", "line": 51, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 51, "endColumn": 22}, {"ruleId": "768", "severity": 1, "message": "798", "line": 56, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 56, "endColumn": 20}, {"ruleId": "768", "severity": 1, "message": "799", "line": 5, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 5, "endColumn": 8}, {"ruleId": "768", "severity": 1, "message": "789", "line": 13, "column": 11, "nodeType": "770", "messageId": "771", "endLine": 13, "endColumn": 19}, {"ruleId": "768", "severity": 1, "message": "796", "line": 21, "column": 11, "nodeType": "770", "messageId": "771", "endLine": 21, "endColumn": 26}, {"ruleId": "768", "severity": 1, "message": "800", "line": 28, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 28, "endColumn": 21}, {"ruleId": "768", "severity": 1, "message": "801", "line": 11, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 11, "endColumn": 19}, {"ruleId": "768", "severity": 1, "message": "789", "line": 13, "column": 11, "nodeType": "770", "messageId": "771", "endLine": 13, "endColumn": 19}, {"ruleId": "768", "severity": 1, "message": "796", "line": 21, "column": 11, "nodeType": "770", "messageId": "771", "endLine": 21, "endColumn": 26}, {"ruleId": "768", "severity": 1, "message": "783", "line": 8, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 8, "endColumn": 7}, {"ruleId": "768", "severity": 1, "message": "784", "line": 9, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 9, "endColumn": 14}, {"ruleId": "768", "severity": 1, "message": "802", "line": 11, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 11, "endColumn": 13}, {"ruleId": "768", "severity": 1, "message": "803", "line": 14, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 14, "endColumn": 17}, {"ruleId": "768", "severity": 1, "message": "804", "line": 25, "column": 8, "nodeType": "770", "messageId": "771", "endLine": 25, "endColumn": 16}, {"ruleId": "768", "severity": 1, "message": "805", "line": 31, "column": 8, "nodeType": "770", "messageId": "771", "endLine": 31, "endColumn": 16}, {"ruleId": "768", "severity": 1, "message": "796", "line": 37, "column": 11, "nodeType": "770", "messageId": "771", "endLine": 37, "endColumn": 26}, {"ruleId": "768", "severity": 1, "message": "806", "line": 39, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 39, "endColumn": 17}, {"ruleId": "768", "severity": 1, "message": "800", "line": 41, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 41, "endColumn": 22}, {"ruleId": "768", "severity": 1, "message": "807", "line": 109, "column": 19, "nodeType": "770", "messageId": "771", "endLine": 109, "endColumn": 29}, {"ruleId": "768", "severity": 1, "message": "808", "line": 117, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 117, "endColumn": 28}, {"ruleId": "768", "severity": 1, "message": "809", "line": 118, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 118, "endColumn": 23}, {"ruleId": "768", "severity": 1, "message": "810", "line": 118, "column": 25, "nodeType": "770", "messageId": "771", "endLine": 118, "endColumn": 41}, {"ruleId": "811", "severity": 1, "message": "812", "line": 472, "column": 6, "nodeType": "813", "endLine": 472, "endColumn": 15, "suggestions": "814"}, {"ruleId": "768", "severity": 1, "message": "815", "line": 477, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 477, "endColumn": 26}, {"ruleId": "768", "severity": 1, "message": "816", "line": 1, "column": 27, "nodeType": "770", "messageId": "771", "endLine": 1, "endColumn": 36}, {"ruleId": "768", "severity": 1, "message": "817", "line": 49, "column": 19, "nodeType": "770", "messageId": "771", "endLine": 49, "endColumn": 26}, {"ruleId": "768", "severity": 1, "message": "788", "line": 15, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 15, "endColumn": 10}, {"ruleId": "768", "severity": 1, "message": "818", "line": 39, "column": 14, "nodeType": "770", "messageId": "771", "endLine": 39, "endColumn": 25}, {"ruleId": "768", "severity": 1, "message": "799", "line": 5, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 5, "endColumn": 8}, {"ruleId": "768", "severity": 1, "message": "800", "line": 31, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 31, "endColumn": 21}, {"ruleId": "768", "severity": 1, "message": "799", "line": 5, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 5, "endColumn": 8}, {"ruleId": "768", "severity": 1, "message": "800", "line": 31, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 31, "endColumn": 21}, {"ruleId": "768", "severity": 1, "message": "799", "line": 5, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 5, "endColumn": 8}, {"ruleId": "768", "severity": 1, "message": "800", "line": 31, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 31, "endColumn": 21}, {"ruleId": "768", "severity": 1, "message": "800", "line": 27, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 27, "endColumn": 21}, {"ruleId": "768", "severity": 1, "message": "799", "line": 5, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 5, "endColumn": 8}, {"ruleId": "768", "severity": 1, "message": "819", "line": 6, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 6, "endColumn": 9}, {"ruleId": "768", "severity": 1, "message": "789", "line": 14, "column": 11, "nodeType": "770", "messageId": "771", "endLine": 14, "endColumn": 19}, {"ruleId": "768", "severity": 1, "message": "796", "line": 23, "column": 11, "nodeType": "770", "messageId": "771", "endLine": 23, "endColumn": 26}, {"ruleId": "768", "severity": 1, "message": "800", "line": 30, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 30, "endColumn": 21}, {"ruleId": "768", "severity": 1, "message": "820", "line": 33, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 33, "endColumn": 29}, {"ruleId": "768", "severity": 1, "message": "821", "line": 38, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 38, "endColumn": 26}, {"ruleId": "768", "severity": 1, "message": "796", "line": 20, "column": 11, "nodeType": "770", "messageId": "771", "endLine": 20, "endColumn": 26}, {"ruleId": "768", "severity": 1, "message": "800", "line": 27, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 27, "endColumn": 21}, {"ruleId": "768", "severity": 1, "message": "821", "line": 35, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 35, "endColumn": 26}, {"ruleId": "768", "severity": 1, "message": "799", "line": 5, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 5, "endColumn": 8}, {"ruleId": "768", "severity": 1, "message": "819", "line": 6, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 6, "endColumn": 9}, {"ruleId": "768", "severity": 1, "message": "789", "line": 14, "column": 11, "nodeType": "770", "messageId": "771", "endLine": 14, "endColumn": 19}, {"ruleId": "768", "severity": 1, "message": "796", "line": 23, "column": 11, "nodeType": "770", "messageId": "771", "endLine": 23, "endColumn": 26}, {"ruleId": "768", "severity": 1, "message": "800", "line": 30, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 30, "endColumn": 21}, {"ruleId": "768", "severity": 1, "message": "820", "line": 33, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 33, "endColumn": 29}, {"ruleId": "768", "severity": 1, "message": "821", "line": 38, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 38, "endColumn": 26}, {"ruleId": "768", "severity": 1, "message": "796", "line": 24, "column": 11, "nodeType": "770", "messageId": "771", "endLine": 24, "endColumn": 26}, {"ruleId": "811", "severity": 1, "message": "822", "line": 53, "column": 6, "nodeType": "813", "endLine": 53, "endColumn": 18, "suggestions": "823"}, {"ruleId": "768", "severity": 1, "message": "824", "line": 1, "column": 8, "nodeType": "770", "messageId": "771", "endLine": 1, "endColumn": 13}, {"ruleId": "768", "severity": 1, "message": "825", "line": 5, "column": 7, "nodeType": "770", "messageId": "771", "endLine": 5, "endColumn": 14}, {"ruleId": "768", "severity": 1, "message": "788", "line": 14, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 14, "endColumn": 10}, {"ruleId": "768", "severity": 1, "message": "826", "line": 28, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 28, "endColumn": 18}, {"ruleId": "768", "severity": 1, "message": "824", "line": 1, "column": 8, "nodeType": "770", "messageId": "771", "endLine": 1, "endColumn": 13}, {"ruleId": "768", "severity": 1, "message": "825", "line": 5, "column": 7, "nodeType": "770", "messageId": "771", "endLine": 5, "endColumn": 14}, {"ruleId": "768", "severity": 1, "message": "783", "line": 8, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 8, "endColumn": 7}, {"ruleId": "768", "severity": 1, "message": "784", "line": 9, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 9, "endColumn": 14}, {"ruleId": "768", "severity": 1, "message": "787", "line": 10, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 10, "endColumn": 14}, {"ruleId": "768", "severity": 1, "message": "827", "line": 23, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 23, "endColumn": 15}, {"ruleId": "768", "severity": 1, "message": "828", "line": 24, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 24, "endColumn": 17}, {"ruleId": "768", "severity": 1, "message": "790", "line": 46, "column": 15, "nodeType": "770", "messageId": "771", "endLine": 46, "endColumn": 27}, {"ruleId": "768", "severity": 1, "message": "829", "line": 47, "column": 12, "nodeType": "770", "messageId": "771", "endLine": 47, "endColumn": 21}, {"ruleId": "811", "severity": 1, "message": "830", "line": 134, "column": 6, "nodeType": "813", "endLine": 134, "endColumn": 18, "suggestions": "831"}, {"ruleId": "768", "severity": 1, "message": "783", "line": 8, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 8, "endColumn": 7}, {"ruleId": "768", "severity": 1, "message": "784", "line": 9, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 9, "endColumn": 14}, {"ruleId": "768", "severity": 1, "message": "787", "line": 10, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 10, "endColumn": 14}, {"ruleId": "768", "severity": 1, "message": "827", "line": 23, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 23, "endColumn": 15}, {"ruleId": "768", "severity": 1, "message": "828", "line": 24, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 24, "endColumn": 17}, {"ruleId": "768", "severity": 1, "message": "788", "line": 25, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 25, "endColumn": 10}, {"ruleId": "768", "severity": 1, "message": "791", "line": 37, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 37, "endColumn": 17}, {"ruleId": "768", "severity": 1, "message": "832", "line": 41, "column": 13, "nodeType": "770", "messageId": "771", "endLine": 41, "endColumn": 23}, {"ruleId": "768", "severity": 1, "message": "790", "line": 43, "column": 15, "nodeType": "770", "messageId": "771", "endLine": 43, "endColumn": 27}, {"ruleId": "768", "severity": 1, "message": "833", "line": 44, "column": 17, "nodeType": "770", "messageId": "771", "endLine": 44, "endColumn": 31}, {"ruleId": "811", "severity": 1, "message": "834", "line": 98, "column": 6, "nodeType": "813", "endLine": 98, "endColumn": 18, "suggestions": "835"}, {"ruleId": "768", "severity": 1, "message": "836", "line": 101, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 101, "endColumn": 27}, {"ruleId": "768", "severity": 1, "message": "783", "line": 8, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 8, "endColumn": 7}, {"ruleId": "768", "severity": 1, "message": "784", "line": 9, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 9, "endColumn": 14}, {"ruleId": "768", "severity": 1, "message": "787", "line": 10, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 10, "endColumn": 14}, {"ruleId": "768", "severity": 1, "message": "827", "line": 23, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 23, "endColumn": 15}, {"ruleId": "768", "severity": 1, "message": "828", "line": 24, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 24, "endColumn": 17}, {"ruleId": "768", "severity": 1, "message": "788", "line": 25, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 25, "endColumn": 10}, {"ruleId": "768", "severity": 1, "message": "802", "line": 29, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 29, "endColumn": 13}, {"ruleId": "768", "severity": 1, "message": "792", "line": 39, "column": 11, "nodeType": "770", "messageId": "771", "endLine": 39, "endColumn": 19}, {"ruleId": "768", "severity": 1, "message": "790", "line": 43, "column": 15, "nodeType": "770", "messageId": "771", "endLine": 43, "endColumn": 27}, {"ruleId": "768", "severity": 1, "message": "837", "line": 44, "column": 14, "nodeType": "770", "messageId": "771", "endLine": 44, "endColumn": 25}, {"ruleId": "768", "severity": 1, "message": "838", "line": 50, "column": 69, "nodeType": "770", "messageId": "771", "endLine": 50, "endColumn": 76}, {"ruleId": "768", "severity": 1, "message": "839", "line": 79, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 79, "endColumn": 26}, {"ruleId": "811", "severity": 1, "message": "840", "line": 145, "column": 6, "nodeType": "813", "endLine": 145, "endColumn": 8, "suggestions": "841"}, {"ruleId": "768", "severity": 1, "message": "842", "line": 689, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 689, "endColumn": 26}, {"ruleId": "768", "severity": 1, "message": "843", "line": 20, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 20, "endColumn": 13}, {"ruleId": "768", "severity": 1, "message": "844", "line": 21, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 21, "endColumn": 9}, {"ruleId": "768", "severity": 1, "message": "845", "line": 22, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 22, "endColumn": 11}, {"ruleId": "768", "severity": 1, "message": "782", "line": 23, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 23, "endColumn": 7}, {"ruleId": "768", "severity": 1, "message": "846", "line": 26, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 26, "endColumn": 17}, {"ruleId": "768", "severity": 1, "message": "847", "line": 69, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 69, "endColumn": 22}, {"ruleId": "848", "severity": 1, "message": "849", "line": 466, "column": 9, "nodeType": "850", "messageId": "851", "endLine": 469, "endColumn": 10}, {"ruleId": "811", "severity": 1, "message": "852", "line": 95, "column": 6, "nodeType": "813", "endLine": 95, "endColumn": 21, "suggestions": "853", "suppressions": "854"}, {"ruleId": "764", "severity": 1, "message": "765", "line": 260, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 264, "endColumn": 11}, {"ruleId": "764", "severity": 1, "message": "765", "line": 274, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 274, "endColumn": 70}, {"ruleId": "764", "severity": 1, "message": "765", "line": 278, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 278, "endColumn": 54}, {"ruleId": "764", "severity": 1, "message": "765", "line": 333, "column": 11, "nodeType": "766", "messageId": "767", "endLine": 338, "endColumn": 13}, {"ruleId": "764", "severity": 1, "message": "765", "line": 435, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 439, "endColumn": 11}, {"ruleId": "764", "severity": 1, "message": "765", "line": 451, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 451, "endColumn": 54}, {"ruleId": "764", "severity": 1, "message": "765", "line": 668, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 668, "endColumn": 163}, {"ruleId": "764", "severity": 1, "message": "765", "line": 677, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 677, "endColumn": 70}, {"ruleId": "764", "severity": 1, "message": "765", "line": 681, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 681, "endColumn": 54}, {"ruleId": "768", "severity": 1, "message": "855", "line": 755, "column": 17, "nodeType": "770", "messageId": "771", "endLine": 755, "endColumn": 22}, {"ruleId": "764", "severity": 1, "message": "765", "line": 775, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 779, "endColumn": 11}, {"ruleId": "764", "severity": 1, "message": "765", "line": 794, "column": 11, "nodeType": "766", "messageId": "767", "endLine": 798, "endColumn": 13}, {"ruleId": "764", "severity": 1, "message": "765", "line": 801, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 804, "endColumn": 11}, {"ruleId": "764", "severity": 1, "message": "765", "line": 810, "column": 11, "nodeType": "766", "messageId": "767", "endLine": 814, "endColumn": 13}, {"ruleId": "764", "severity": 1, "message": "765", "line": 817, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 820, "endColumn": 11}, {"ruleId": "764", "severity": 1, "message": "765", "line": 885, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 889, "endColumn": 11}, {"ruleId": "856", "severity": 1, "message": "857", "line": 955, "column": 3, "nodeType": "858", "messageId": "859", "endLine": 955, "endColumn": 29}, {"ruleId": "856", "severity": 1, "message": "860", "line": 1143, "column": 3, "nodeType": "858", "messageId": "859", "endLine": 1143, "endColumn": 23}, {"ruleId": "856", "severity": 1, "message": "861", "line": 1238, "column": 3, "nodeType": "858", "messageId": "859", "endLine": 1238, "endColumn": 20}, {"ruleId": "764", "severity": 1, "message": "765", "line": 1287, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 1287, "endColumn": 163}, {"ruleId": "764", "severity": 1, "message": "765", "line": 1317, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 1317, "endColumn": 163}, {"ruleId": "764", "severity": 1, "message": "765", "line": 1370, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 1370, "endColumn": 163}, {"ruleId": "764", "severity": 1, "message": "765", "line": 1412, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 1412, "endColumn": 163}, {"ruleId": "768", "severity": 1, "message": "862", "line": 6, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 6, "endColumn": 8}, {"ruleId": "768", "severity": 1, "message": "788", "line": 11, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 11, "endColumn": 10}, {"ruleId": "768", "severity": 1, "message": "824", "line": 1, "column": 8, "nodeType": "770", "messageId": "771", "endLine": 1, "endColumn": 13}, {"ruleId": "768", "severity": 1, "message": "825", "line": 5, "column": 7, "nodeType": "770", "messageId": "771", "endLine": 5, "endColumn": 14}, {"ruleId": "768", "severity": 1, "message": "863", "line": 3, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 3, "endColumn": 11}, {"ruleId": "768", "severity": 1, "message": "864", "line": 4, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 4, "endColumn": 6}, {"ruleId": "768", "severity": 1, "message": "865", "line": 5, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 5, "endColumn": 7}, {"ruleId": "768", "severity": 1, "message": "866", "line": 6, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 6, "endColumn": 11}, {"ruleId": "768", "severity": 1, "message": "867", "line": 7, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 7, "endColumn": 6}, {"ruleId": "768", "severity": 1, "message": "868", "line": 12, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 12, "endColumn": 9}, {"ruleId": "768", "severity": 1, "message": "869", "line": 36, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 36, "endColumn": 21}, {"ruleId": "768", "severity": 1, "message": "870", "line": 50, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 50, "endColumn": 17}, {"ruleId": "768", "severity": 1, "message": "871", "line": 64, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 64, "endColumn": 20}, {"ruleId": "768", "severity": 1, "message": "872", "line": 88, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 88, "endColumn": 22}, {"ruleId": "768", "severity": 1, "message": "873", "line": 104, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 104, "endColumn": 30}, {"ruleId": "768", "severity": 1, "message": "874", "line": 3, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 3, "endColumn": 12}, {"ruleId": "768", "severity": 1, "message": "866", "line": 3, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 3, "endColumn": 11}, {"ruleId": "768", "severity": 1, "message": "867", "line": 4, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 4, "endColumn": 6}, {"ruleId": "768", "severity": 1, "message": "875", "line": 5, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 5, "endColumn": 8}, {"ruleId": "768", "severity": 1, "message": "876", "line": 6, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 6, "endColumn": 8}, {"ruleId": "768", "severity": 1, "message": "877", "line": 7, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 7, "endColumn": 16}, {"ruleId": "768", "severity": 1, "message": "878", "line": 8, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 8, "endColumn": 10}, {"ruleId": "768", "severity": 1, "message": "868", "line": 9, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 9, "endColumn": 9}, {"ruleId": "768", "severity": 1, "message": "879", "line": 10, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 10, "endColumn": 22}, {"ruleId": "768", "severity": 1, "message": "863", "line": 11, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 11, "endColumn": 11}, {"ruleId": "768", "severity": 1, "message": "864", "line": 12, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 12, "endColumn": 6}, {"ruleId": "768", "severity": 1, "message": "865", "line": 13, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 13, "endColumn": 7}, {"ruleId": "768", "severity": 1, "message": "880", "line": 14, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 14, "endColumn": 16}, {"ruleId": "768", "severity": 1, "message": "881", "line": 15, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 15, "endColumn": 7}, {"ruleId": "768", "severity": 1, "message": "874", "line": 16, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 16, "endColumn": 12}, {"ruleId": "768", "severity": 1, "message": "882", "line": 18, "column": 40, "nodeType": "770", "messageId": "771", "endLine": 18, "endColumn": 44}, {"ruleId": "768", "severity": 1, "message": "883", "line": 47, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 47, "endColumn": 19}, {"ruleId": "768", "severity": 1, "message": "884", "line": 64, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 64, "endColumn": 19}, {"ruleId": "768", "severity": 1, "message": "885", "line": 71, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 71, "endColumn": 20}, {"ruleId": "768", "severity": 1, "message": "872", "line": 79, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 79, "endColumn": 22}, {"ruleId": "768", "severity": 1, "message": "873", "line": 95, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 95, "endColumn": 30}, {"ruleId": "768", "severity": 1, "message": "886", "line": 290, "column": 27, "nodeType": "770", "messageId": "771", "endLine": 290, "endColumn": 37}, {"ruleId": "768", "severity": 1, "message": "887", "line": 291, "column": 27, "nodeType": "770", "messageId": "771", "endLine": 291, "endColumn": 36}, {"ruleId": "768", "severity": 1, "message": "799", "line": 3, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 3, "endColumn": 8}, {"ruleId": "811", "severity": 1, "message": "888", "line": 54, "column": 6, "nodeType": "813", "endLine": 54, "endColumn": 34, "suggestions": "889"}, {"ruleId": "768", "severity": 1, "message": "890", "line": 25, "column": 13, "nodeType": "770", "messageId": "771", "endLine": 25, "endColumn": 25}, {"ruleId": "768", "severity": 1, "message": "891", "line": 33, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 33, "endColumn": 15}, {"ruleId": "768", "severity": 1, "message": "892", "line": 34, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 34, "endColumn": 14}, {"ruleId": "768", "severity": 1, "message": "893", "line": 35, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 35, "endColumn": 22}, {"ruleId": "768", "severity": 1, "message": "894", "line": 36, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 36, "endColumn": 21}, {"ruleId": "768", "severity": 1, "message": "895", "line": 37, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 37, "endColumn": 17}, {"ruleId": "768", "severity": 1, "message": "896", "line": 41, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 41, "endColumn": 20}, {"ruleId": "768", "severity": 1, "message": "897", "line": 43, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 43, "endColumn": 34}, {"ruleId": "768", "severity": 1, "message": "898", "line": 69, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 69, "endColumn": 17}, {"ruleId": "768", "severity": 1, "message": "899", "line": 69, "column": 19, "nodeType": "770", "messageId": "771", "endLine": 69, "endColumn": 29}, {"ruleId": "811", "severity": 1, "message": "900", "line": 88, "column": 6, "nodeType": "813", "endLine": 88, "endColumn": 18, "suggestions": "901"}, {"ruleId": "811", "severity": 1, "message": "902", "line": 448, "column": 6, "nodeType": "813", "endLine": 448, "endColumn": 28, "suggestions": "903"}, {"ruleId": "768", "severity": 1, "message": "904", "line": 4, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 4, "endColumn": 12}, {"ruleId": "768", "severity": 1, "message": "905", "line": 8, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 8, "endColumn": 7}, {"ruleId": "768", "severity": 1, "message": "906", "line": 9, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 9, "endColumn": 11}, {"ruleId": "768", "severity": 1, "message": "907", "line": 10, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 10, "endColumn": 15}, {"ruleId": "811", "severity": 1, "message": "888", "line": 46, "column": 6, "nodeType": "813", "endLine": 46, "endColumn": 18, "suggestions": "908"}, {"ruleId": "768", "severity": 1, "message": "909", "line": 9, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 9, "endColumn": 14}, {"ruleId": "768", "severity": 1, "message": "843", "line": 10, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 10, "endColumn": 13}, {"ruleId": "768", "severity": 1, "message": "844", "line": 11, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 11, "endColumn": 9}, {"ruleId": "768", "severity": 1, "message": "845", "line": 12, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 12, "endColumn": 11}, {"ruleId": "768", "severity": 1, "message": "907", "line": 33, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 33, "endColumn": 15}, {"ruleId": "768", "severity": 1, "message": "910", "line": 35, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 35, "endColumn": 26}, {"ruleId": "768", "severity": 1, "message": "837", "line": 42, "column": 14, "nodeType": "770", "messageId": "771", "endLine": 42, "endColumn": 25}, {"ruleId": "768", "severity": 1, "message": "891", "line": 52, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 52, "endColumn": 15}, {"ruleId": "768", "severity": 1, "message": "892", "line": 53, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 53, "endColumn": 14}, {"ruleId": "768", "severity": 1, "message": "893", "line": 54, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 54, "endColumn": 22}, {"ruleId": "768", "severity": 1, "message": "894", "line": 55, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 55, "endColumn": 21}, {"ruleId": "768", "severity": 1, "message": "895", "line": 56, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 56, "endColumn": 17}, {"ruleId": "768", "severity": 1, "message": "911", "line": 57, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 57, "endColumn": 15}, {"ruleId": "768", "severity": 1, "message": "912", "line": 58, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 58, "endColumn": 19}, {"ruleId": "768", "severity": 1, "message": "913", "line": 59, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 59, "endColumn": 21}, {"ruleId": "768", "severity": 1, "message": "806", "line": 72, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 72, "endColumn": 17}, {"ruleId": "768", "severity": 1, "message": "914", "line": 79, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 79, "endColumn": 23}, {"ruleId": "768", "severity": 1, "message": "915", "line": 79, "column": 25, "nodeType": "770", "messageId": "771", "endLine": 79, "endColumn": 41}, {"ruleId": "768", "severity": 1, "message": "916", "line": 80, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 80, "endColumn": 27}, {"ruleId": "768", "severity": 1, "message": "917", "line": 85, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 85, "endColumn": 26}, {"ruleId": "811", "severity": 1, "message": "888", "line": 105, "column": 6, "nodeType": "813", "endLine": 105, "endColumn": 18, "suggestions": "918"}, {"ruleId": "811", "severity": 1, "message": "919", "line": 112, "column": 6, "nodeType": "813", "endLine": 112, "endColumn": 20, "suggestions": "920"}, {"ruleId": "811", "severity": 1, "message": "921", "line": 127, "column": 6, "nodeType": "813", "endLine": 127, "endColumn": 34, "suggestions": "922"}, {"ruleId": "768", "severity": 1, "message": "923", "line": 283, "column": 13, "nodeType": "770", "messageId": "771", "endLine": 283, "endColumn": 19}, {"ruleId": "768", "severity": 1, "message": "846", "line": 17, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 17, "endColumn": 17}, {"ruleId": "768", "severity": 1, "message": "907", "line": 34, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 34, "endColumn": 15}, {"ruleId": "768", "severity": 1, "message": "910", "line": 35, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 35, "endColumn": 26}, {"ruleId": "768", "severity": 1, "message": "924", "line": 39, "column": 11, "nodeType": "770", "messageId": "771", "endLine": 39, "endColumn": 19}, {"ruleId": "768", "severity": 1, "message": "891", "line": 51, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 51, "endColumn": 15}, {"ruleId": "768", "severity": 1, "message": "892", "line": 52, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 52, "endColumn": 14}, {"ruleId": "768", "severity": 1, "message": "894", "line": 54, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 54, "endColumn": 21}, {"ruleId": "768", "severity": 1, "message": "895", "line": 55, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 55, "endColumn": 17}, {"ruleId": "768", "severity": 1, "message": "897", "line": 62, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 62, "endColumn": 34}, {"ruleId": "768", "severity": 1, "message": "925", "line": 105, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 105, "endColumn": 26}, {"ruleId": "768", "severity": 1, "message": "926", "line": 105, "column": 28, "nodeType": "770", "messageId": "771", "endLine": 105, "endColumn": 47}, {"ruleId": "811", "severity": 1, "message": "919", "line": 145, "column": 6, "nodeType": "813", "endLine": 145, "endColumn": 18, "suggestions": "927"}, {"ruleId": "768", "severity": 1, "message": "928", "line": 701, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 701, "endColumn": 19}, {"ruleId": "768", "severity": 1, "message": "929", "line": 1311, "column": 11, "nodeType": "770", "messageId": "771", "endLine": 1311, "endColumn": 28}, {"ruleId": "768", "severity": 1, "message": "930", "line": 1316, "column": 11, "nodeType": "770", "messageId": "771", "endLine": 1316, "endColumn": 30}, {"ruleId": "768", "severity": 1, "message": "931", "line": 1883, "column": 9, "nodeType": "770", "messageId": "771", "endLine": 1883, "endColumn": 23}, {"ruleId": "768", "severity": 1, "message": "824", "line": 1, "column": 8, "nodeType": "770", "messageId": "771", "endLine": 1, "endColumn": 13}, {"ruleId": "768", "severity": 1, "message": "825", "line": 5, "column": 7, "nodeType": "770", "messageId": "771", "endLine": 5, "endColumn": 14}, {"ruleId": "768", "severity": 1, "message": "824", "line": 1, "column": 8, "nodeType": "770", "messageId": "771", "endLine": 1, "endColumn": 13}, {"ruleId": "768", "severity": 1, "message": "825", "line": 5, "column": 7, "nodeType": "770", "messageId": "771", "endLine": 5, "endColumn": 14}, {"ruleId": "768", "severity": 1, "message": "824", "line": 1, "column": 8, "nodeType": "770", "messageId": "771", "endLine": 1, "endColumn": 13}, {"ruleId": "768", "severity": 1, "message": "825", "line": 5, "column": 7, "nodeType": "770", "messageId": "771", "endLine": 5, "endColumn": 14}, {"ruleId": "768", "severity": 1, "message": "824", "line": 1, "column": 8, "nodeType": "770", "messageId": "771", "endLine": 1, "endColumn": 13}, {"ruleId": "768", "severity": 1, "message": "825", "line": 5, "column": 7, "nodeType": "770", "messageId": "771", "endLine": 5, "endColumn": 14}, {"ruleId": "768", "severity": 1, "message": "932", "line": 83, "column": 13, "nodeType": "770", "messageId": "771", "endLine": 83, "endColumn": 21}, {"ruleId": "764", "severity": 1, "message": "765", "line": 109, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 109, "endColumn": 163}, {"ruleId": "764", "severity": 1, "message": "765", "line": 123, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 123, "endColumn": 70}, {"ruleId": "764", "severity": 1, "message": "765", "line": 127, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 127, "endColumn": 54}, {"ruleId": "764", "severity": 1, "message": "765", "line": 212, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 212, "endColumn": 163}, {"ruleId": "764", "severity": 1, "message": "765", "line": 226, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 226, "endColumn": 70}, {"ruleId": "764", "severity": 1, "message": "765", "line": 230, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 230, "endColumn": 54}, {"ruleId": "764", "severity": 1, "message": "765", "line": 271, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 271, "endColumn": 163}, {"ruleId": "764", "severity": 1, "message": "765", "line": 280, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 280, "endColumn": 70}, {"ruleId": "764", "severity": 1, "message": "765", "line": 284, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 284, "endColumn": 54}, {"ruleId": "764", "severity": 1, "message": "765", "line": 320, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 320, "endColumn": 70}, {"ruleId": "764", "severity": 1, "message": "765", "line": 324, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 324, "endColumn": 54}, {"ruleId": "764", "severity": 1, "message": "765", "line": 360, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 360, "endColumn": 163}, {"ruleId": "764", "severity": 1, "message": "765", "line": 369, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 369, "endColumn": 70}, {"ruleId": "764", "severity": 1, "message": "765", "line": 373, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 373, "endColumn": 54}, {"ruleId": "764", "severity": 1, "message": "765", "line": 450, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 450, "endColumn": 163}, {"ruleId": "764", "severity": 1, "message": "765", "line": 459, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 459, "endColumn": 70}, {"ruleId": "764", "severity": 1, "message": "765", "line": 463, "column": 9, "nodeType": "766", "messageId": "767", "endLine": 463, "endColumn": 54}, {"ruleId": "768", "severity": 1, "message": "933", "line": 12, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 12, "endColumn": 19}, {"ruleId": "768", "severity": 1, "message": "791", "line": 27, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 27, "endColumn": 17}, {"ruleId": "768", "severity": 1, "message": "804", "line": 30, "column": 11, "nodeType": "770", "messageId": "771", "endLine": 30, "endColumn": 19}, {"ruleId": "768", "severity": 1, "message": "893", "line": 34, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 34, "endColumn": 29}, {"ruleId": "768", "severity": 1, "message": "898", "line": 49, "column": 10, "nodeType": "770", "messageId": "771", "endLine": 49, "endColumn": 17}, {"ruleId": "768", "severity": 1, "message": "899", "line": 49, "column": 19, "nodeType": "770", "messageId": "771", "endLine": 49, "endColumn": 29}, {"ruleId": "811", "severity": 1, "message": "888", "line": 64, "column": 6, "nodeType": "813", "endLine": 64, "endColumn": 32, "suggestions": "934"}, {"ruleId": "768", "severity": 1, "message": "935", "line": 270, "column": 17, "nodeType": "770", "messageId": "771", "endLine": 270, "endColumn": 23}, {"ruleId": "768", "severity": 1, "message": "936", "line": 17, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 17, "endColumn": 8}, {"ruleId": "768", "severity": 1, "message": "845", "line": 16, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 16, "endColumn": 11}, {"ruleId": "768", "severity": 1, "message": "844", "line": 17, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 17, "endColumn": 9}, {"ruleId": "768", "severity": 1, "message": "843", "line": 19, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 19, "endColumn": 13}, {"ruleId": "768", "severity": 1, "message": "804", "line": 14, "column": 11, "nodeType": "770", "messageId": "771", "endLine": 14, "endColumn": 19}, {"ruleId": "768", "severity": 1, "message": "802", "line": 12, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 12, "endColumn": 13}, {"ruleId": "768", "severity": 1, "message": "937", "line": 3, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 3, "endColumn": 6}, {"ruleId": "768", "severity": 1, "message": "788", "line": 9, "column": 3, "nodeType": "770", "messageId": "771", "endLine": 9, "endColumn": 10}, "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "no-unused-vars", "'Avatar' is defined but never used.", "Identifier", "unusedVar", "'AdminIcon' is defined but never used.", "'ConstructionIcon' is defined but never used.", "'CableIcon' is defined but never used.", "'ReportIcon' is defined but never used.", "'homeAnchorEl' is assigned a value but never used.", "'adminAnchorEl' is assigned a value but never used.", "'cantieriAnchorEl' is assigned a value but never used.", "'caviAnchorEl' is assigned a value but never used.", "'selectedCantiereName' is assigned a value but never used.", "'React' is defined but never used.", "'Grid' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardActionArea' is defined but never used.", "'navigateTo' is assigned a value but never used.", "'CardActions' is defined but never used.", "'Divider' is defined but never used.", "'HomeIcon' is defined but never used.", "'ViewListIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'EditIcon' is defined but never used.", "'DeleteIcon' is defined but never used.", "'HistoryIcon' is defined but never used.", "'ParcoCavi' is defined but never used.", "'isImpersonating' is assigned a value but never used.", "'handleSuccess' is assigned a value but never used.", "'handleError' is assigned a value but never used.", "'Paper' is defined but never used.", "'cantiereName' is assigned a value but never used.", "'lastCheck' is assigned a value but never used.", "'IconButton' is defined but never used.", "'LinearProgress' is defined but never used.", "'InfoIcon' is defined but never used.", "'CavoForm' is defined but never used.", "'navigate' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'statiInstallazione' is assigned a value but never used.", "'tipologieCavi' is assigned a value but never used.", "'setTipologieCavi' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'error' and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["938"], "'handleOpenDetails' is assigned a value but never used.", "'useEffect' is defined but never used.", "'useAuth' is assigned a value but never used.", "'RefreshIcon' is defined but never used.", "'Button' is defined but never used.", "'handleBackToCantieri' is assigned a value but never used.", "'handleBackToAdmin' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array.", ["939"], "'axios' is defined but never used.", "'API_URL' is assigned a value but never used.", "'filePath' is assigned a value but never used.", "'ListItemIcon' is defined but never used.", "'ListItemButton' is defined but never used.", "'BuildIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCertificazioni'. Either include it or remove the dependency array.", ["940"], "'SearchIcon' is defined but never used.", "'AssignmentIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadComande'. Either include it or remove the dependency array.", ["941"], "'handleOptionSelect' is assigned a value but never used.", "'WarningIcon' is defined but never used.", "'isEmpty' is defined but never used.", "'isFirstInsertion' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array.", ["942"], "'renderBobineCards' is assigned a value but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'FormHelperText' is defined but never used.", "'formWarnings' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "IfStatement", "unreachableCode", "React Hook React.useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["943"], ["944"], "'token' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'updateCavoForCompatibility'.", "ObjectExpression", "unexpected", "Duplicate key 'getRevisioneCorrente'.", "Duplicate key 'getCaviInstallati'.", "'Stack' is defined but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'Legend' is defined but never used.", "'progressData' is assigned a value but never used.", "'caviData' is assigned a value but never used.", "'metricsData' is assigned a value but never used.", "'CustomTooltip' is assigned a value but never used.", "'renderCustomizedLabel' is assigned a value but never used.", "'LineChart' is defined but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'Tooltip' is defined but never used.", "'ResponsiveContainer' is defined but never used.", "'ComposedChart' is defined but never used.", "'Line' is defined but never used.", "'Chip' is defined but never used.", "'bobineData' is assigned a value but never used.", "'totaliData' is assigned a value but never used.", "'analisiData' is assigned a value but never used.", "'isCompleto' is assigned a value but never used.", "'isInCorso' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["945"], "'DownloadIcon' is defined but never used.", "'CABLE_STATES' is defined but never used.", "'REEL_STATES' is defined but never used.", "'determineCableState' is defined but never used.", "'determineReelState' is defined but never used.", "'canModifyCable' is defined but never used.", "'getReelStateColor' is defined but never used.", "'redirectToVisualizzaCavi' is defined but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array.", ["946"], "React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array.", ["947"], "'TextField' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", ["948"], "'FormControl' is defined but never used.", "'ListItemSecondaryAction' is defined but never used.", "'isCableSpare' is defined but never used.", "'isCableInstalled' is defined but never used.", "'getCableStateColor' is defined but never used.", "'searchResults' is assigned a value but never used.", "'setSearchResults' is assigned a value but never used.", "'showSearchResults' is assigned a value but never used.", "'compatibleBobine' is assigned a value but never used.", ["949"], "React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array.", ["950"], "React Hook useEffect has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["951"], "'bobina' is assigned a value but never used.", "'SaveIcon' is defined but never used.", "'incompatibleReel' is assigned a value but never used.", "'setIncompatibleReel' is assigned a value but never used.", ["952"], "'handleBack' is assigned a value but never used.", "'buildFullBobinaId' is assigned a value but never used.", "'hasSufficientMeters' is assigned a value but never used.", "'getStepContent' is assigned a value but never used.", "'sentData' is assigned a value but never used.", "'FormControlLabel' is defined but never used.", ["953"], "'result' is assigned a value but never used.", "'Alert' is defined but never used.", "'Box' is defined but never used.", {"desc": "954", "fix": "955"}, {"desc": "956", "fix": "957"}, {"desc": "958", "fix": "959"}, {"desc": "960", "fix": "961"}, {"desc": "962", "fix": "963"}, {"desc": "964", "fix": "965"}, {"kind": "966", "justification": "967"}, {"desc": "968", "fix": "969"}, {"desc": "970", "fix": "971"}, {"desc": "972", "fix": "973"}, {"desc": "974", "fix": "975"}, {"desc": "974", "fix": "976"}, {"desc": "977", "fix": "978"}, {"desc": "979", "fix": "980"}, {"desc": "981", "fix": "982"}, {"desc": "983", "fix": "984"}, "Update the dependencies array to be: [error, filters, user]", {"range": "985", "text": "986"}, "Update the dependencies array to be: [cantiereId, selectCantiere]", {"range": "987", "text": "988"}, "Update the dependencies array to be: [cantiereId, loadCertificazioni]", {"range": "989", "text": "990"}, "Update the dependencies array to be: [cantiereId, loadComande]", {"range": "991", "text": "992"}, "Update the dependencies array to be: [handleOptionSelect, initialOption, loadBobine]", {"range": "993", "text": "994"}, "Update the dependencies array to be: [initialOption, loadCavi]", {"range": "995", "text": "996"}, "directive", "", "Update the dependencies array to be: [certificazione, cantiereId, loadCavi]", {"range": "997", "text": "998"}, "Update the dependencies array to be: [cantiereId, loadBobine, loadCavi]", {"range": "999", "text": "1000"}, "Update the dependencies array to be: [selectedCavo, bobine, filterCompatibleBobine]", {"range": "1001", "text": "1002"}, "Update the dependencies array to be: [cantiereId, loadCavi]", {"range": "1003", "text": "1004"}, {"range": "1005", "text": "1004"}, "Update the dependencies array to be: [loadBobine, selectedCavo]", {"range": "1006", "text": "1007"}, "Update the dependencies array to be: [cavoId, cavi, selectedCavo, onError]", {"range": "1008", "text": "1009"}, "Update the dependencies array to be: [cantiereId, loadBobine]", {"range": "1010", "text": "1011"}, "Update the dependencies array to be: [open, bobina, cantiereId, loadCavi]", {"range": "1012", "text": "1013"}, [19729, 19738], "[error, filters, user]", [1559, 1571], "[cantiereId, selectCantiere]", [3538, 3550], "[cantiereId, loadCertificazioni]", [2335, 2347], "[cantiereId, loadComande]", [4642, 4644], "[handleOptionSelect, initialOption, loadBobine]", [3043, 3058], "[initialOption, loadCavi]", [1578, 1606], "[certificazione, cantiereId, loadCavi]", [2572, 2584], "[cantiereId, loadBobine, loadCavi]", [14450, 14472], "[selectedCavo, bobine, filterCompatibleBobine]", [1014, 1026], "[cantiereId, loadCavi]", [3142, 3154], [3288, 3302], "[load<PERSON><PERSON><PERSON>, selected<PERSON>avo]", [3868, 3896], "[cavoId, cavi, selected<PERSON>av<PERSON>, onError]", [4325, 4337], "[cantiereId, loadBobine]", [1912, 1938], "[open, bobina, cantiereId, loadCavi]"]