#!/usr/bin/env python3
"""
Script per testare direttamente la funzione generate_boq_data
"""

import sys
import os
import asyncio

# Aggiungi il percorso del backend al PYTHONPATH
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

async def test_generate_boq_data():
    """Testa direttamente la funzione generate_boq_data."""
    
    print("=== TEST FUNZIONE generate_boq_data ===\n")
    
    try:
        # Import delle funzioni necessarie
        from modules.database_pg import Database
        from api.reports_simple import generate_boq_data
        
        db = Database()
        cantiere_id = 1
        
        print(f"🔍 Testando generate_boq_data per cantiere {cantiere_id}")
        
        # Chiama la funzione direttamente
        result = await generate_boq_data(cantiere_id, db)
        
        print(f"✅ Funzione eseguita con successo!")
        print(f"Nome cantiere: {result.get('nome_cantiere', 'N/A')}")
        
        distinta = result.get('distinta_materiali', [])
        print(f"Numero categorie: {len(distinta)}")
        
        if len(distinta) == 0:
            print("❌ NESSUNA CATEGORIA TROVATA!")
            print("Questo indica un problema nella query o nella logica della funzione.")
        else:
            print(f"✅ Trovate {len(distinta)} categorie:")
            
            for i, item in enumerate(distinta):
                print(f"  {i+1}. {item.get('tipologia', 'N/A')} | {item.get('formazione', 'N/A')}")
                print(f"     Metri acquistati: {item.get('metri_acquistati', 0)}")
                print(f"     Metri residui: {item.get('metri_residui', 0)}")
                print(f"     Metri da posare: {item.get('metri_da_posare', 0)}")
                print(f"     Metri mancanti: {item.get('metri_mancanti', 0)}")
                print(f"     Necessita acquisto: {item.get('necessita_acquisto', True)}")
                
                # Caso specifico
                if item.get('tipologia') == "FG16OR16" and "240MM2" in str(item.get('formazione', '')):
                    print(f"     *** CASO SPECIFICO TROVATO! ***")
                print()
        
        # Riepilogo
        riepilogo = result.get('riepilogo', {})
        print(f"📊 RIEPILOGO:")
        print(f"  Totale categorie: {riepilogo.get('totale_categorie', 0)}")
        print(f"  Totale metri acquistati: {riepilogo.get('totale_metri_acquistati', 0)}")
        print(f"  Totale metri residui: {riepilogo.get('totale_metri_residui', 0)}")
        print(f"  Totale metri mancanti: {riepilogo.get('totale_metri_mancanti', 0)}")
        
    except Exception as e:
        print(f"❌ Errore durante il test: {e}")
        import traceback
        traceback.print_exc()
        
        # Proviamo a testare le query singolarmente
        print("\n🔍 TEST QUERY SINGOLE:")
        try:
            from modules.database_pg import Database
            db = Database()
            
            # Test query cavi
            print("\n1. TEST QUERY CAVI:")
            cavi_query = """
                SELECT
                    tipologia,
                    sezione as formazione,
                    COUNT(*) as num_cavi,
                    COUNT(CASE WHEN stato_installazione != 'Installato' THEN 1 END) as num_cavi_rimanenti,
                    SUM(metri_teorici) as metri_teorici_totali,
                    SUM(CASE WHEN stato_installazione = 'Installato' THEN metratura_reale ELSE 0 END) as metri_reali_posati,
                    SUM(CASE WHEN stato_installazione != 'Installato' THEN metri_teorici ELSE 0 END) as metri_da_posare
                FROM cavi
                WHERE id_cantiere = %s AND (modificato_manualmente IS NULL OR modificato_manualmente != 3)
                GROUP BY tipologia, sezione
                ORDER BY tipologia, sezione
            """
            
            cavi_result = db.execute_query(cavi_query, (1,), fetch_all=True)
            print(f"   Risultati query cavi: {len(cavi_result)} righe")
            for row in cavi_result[:3]:  # Mostra solo le prime 3
                print(f"   {row}")
            
            # Test query bobine
            print("\n2. TEST QUERY BOBINE:")
            bobine_query = """
                SELECT
                    tipologia,
                    sezione as formazione,
                    COUNT(*) as num_bobine,
                    SUM(metri_totali) as metri_acquistati,
                    SUM(metri_residui) as metri_residui
                FROM parco_cavi
                WHERE id_cantiere = %s AND stato_bobina != 'TERMINATA'
                GROUP BY tipologia, sezione
                ORDER BY tipologia, sezione
            """
            
            bobine_result = db.execute_query(bobine_query, (1,), fetch_all=True)
            print(f"   Risultati query bobine: {len(bobine_result)} righe")
            for row in bobine_result:
                print(f"   {row}")
                
        except Exception as query_error:
            print(f"❌ Errore nelle query singole: {query_error}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_generate_boq_data())
