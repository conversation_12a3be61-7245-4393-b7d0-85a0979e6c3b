#!/usr/bin/env python3
"""
Script per testare la correzione del matching case-insensitive nel report BOQ
"""

import psycopg2
from psycopg2.extras import RealDictCursor

# Configurazione database
DB_HOST = "localhost"
DB_PORT = "5432"
DB_NAME = "cantieri"
DB_USER = "postgres"
DB_PASSWORD = "Taranto"

def connect_to_db():
    """Connessione al database PostgreSQL."""
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD,
            cursor_factory=RealDictCursor
        )
        print(f"✅ Connesso al database {DB_NAME}")
        return conn
    except Exception as e:
        print(f"❌ Errore durante la connessione al database: {e}")
        return None

def test_matching_logic():
    """Testa la logica di matching case-insensitive."""

    print("=== TEST MATCHING CASE-INSENSITIVE ===\n")

    conn = connect_to_db()
    if not conn:
        return

    try:
        with conn.cursor() as cur:
            cantiere_id = 1

            # Test 1: Verifica dati bobine
            print("1. BOBINE DISPONIBILI:")
            cur.execute("""
                SELECT tipologia, sezione, metri_residui, stato_bobina
                FROM parco_cavi
                WHERE id_cantiere = %s AND stato_bobina != 'TERMINATA' AND metri_residui > 0
                ORDER BY tipologia, sezione
            """, (cantiere_id,))

            bobine = cur.fetchall()
            for bobina in bobine:
                print(f"  {bobina['tipologia']} | {bobina['sezione']} | {bobina['metri_residui']}m | {bobina['stato_bobina']}")

            # Test 2: Verifica dati cavi
            print("\n2. CAVI DA INSTALLARE:")
            cur.execute("""
                SELECT tipologia, sezione, COUNT(*) as num_cavi,
                       SUM(CASE WHEN stato_installazione != 'Installato' THEN metri_teorici ELSE 0 END) as metri_da_posare
                FROM cavi
                WHERE id_cantiere = %s AND modificato_manualmente != 3
                GROUP BY tipologia, sezione
                HAVING SUM(CASE WHEN stato_installazione != 'Installato' THEN metri_teorici ELSE 0 END) > 0
                ORDER BY tipologia, sezione
            """, (cantiere_id,))

            cavi = cur.fetchall()
            for cavo in cavi:
                print(f"  {cavo['tipologia']} | {cavo['sezione']} | {cavo['num_cavi']} cavi | {cavo['metri_da_posare']}m")

            # Test 3: Nuova logica BOQ con Metri Acquistati e Metri Residui
            print("\n3. TEST NUOVA LOGICA BOQ:")

            # Crea dizionario bobine normalizzato con metri acquistati e residui
            bobine_lookup = {}
            for bobina in bobine:
                tipologia_norm = (bobina['tipologia'] or "").upper().strip()
                sezione_norm = (bobina['sezione'] or "").upper().strip()
                key = f"{tipologia_norm}_{sezione_norm}"
                # Assumiamo che metri_totali = metri_residui per bobine nuove
                bobine_lookup[key] = {
                    'metri_acquistati': bobina['metri_residui'],  # Per test, assumiamo che siano uguali
                    'metri_residui': bobina['metri_residui']
                }

            print(f"  Bobine normalizzate: {list(bobine_lookup.keys())}")

            # Testa nuova logica per ogni cavo
            for cavo in cavi:
                tipologia_norm = (cavo['tipologia'] or "").upper().strip()
                sezione_norm = (cavo['sezione'] or "").upper().strip()
                key = f"{tipologia_norm}_{sezione_norm}"

                bobina_info = bobine_lookup.get(key, {'metri_acquistati': 0, 'metri_residui': 0})
                metri_acquistati = bobina_info['metri_acquistati']
                metri_residui = bobina_info['metri_residui']
                metri_da_posare = cavo['metri_da_posare']

                # Nuova logica: Metri Mancanti = Metri da Posare - Metri Residui
                metri_mancanti = max(0, metri_da_posare - metri_residui)
                necessita_acquisto = metri_mancanti > 0

                match_status = "✅ SUFFICIENTE" if not necessita_acquisto else "❌ INSUFFICIENTE"

                print(f"  {cavo['tipologia']} | {cavo['sezione']}:")
                print(f"    Metri da posare: {metri_da_posare}m")
                print(f"    Metri acquistati: {metri_acquistati}m")
                print(f"    Metri residui: {metri_residui}m")
                print(f"    Metri mancanti: {metri_mancanti}m")
                print(f"    Status: {match_status}")

                # Caso specifico FG16OR16 240MM2
                if "FG16OR16" in tipologia_norm and "240MM2" in sezione_norm:
                    print(f"    *** CASO TEST SPECIFICO ***")
                    print(f"    🎯 NUOVA LOGICA APPLICATA:")
                    print(f"    - Metri da posare: {metri_da_posare}m")
                    print(f"    - Metri residui disponibili: {metri_residui}m")
                    print(f"    - Metri mancanti: {metri_mancanti}m")
                    print(f"    - Necessita acquisto: {'Sì' if necessita_acquisto else 'No'}")
                    if not necessita_acquisto:
                        print(f"    🎉 LOGICA CORRETTA: Non serve acquistare!")
                    else:
                        print(f"    ⚠️ SERVE ACQUISTARE: {metri_mancanti}m aggiuntivi")
                print()

    except Exception as e:
        print(f"❌ Errore durante il test: {e}")
        import traceback
        traceback.print_exc()
    finally:
        conn.close()

if __name__ == "__main__":
    test_matching_logic()
